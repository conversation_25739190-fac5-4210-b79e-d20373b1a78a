package router

import (
	"hospital-management/internal/config"
	"hospital-management/internal/controllers"
	"hospital-management/internal/middleware"
	"hospital-management/internal/services"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/gorm"
)

func Setup(db *gorm.DB, cfg *config.Config) *gin.Engine {
	r := gin.Default()

	// 中间件
	r.Use(middleware.CORS())
	r.Use(middleware.Logger())
	r.Use(middleware.Recovery())

	// Swagger文档
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Hospital Management System API is running",
		})
	})

	// 初始化服务
	authService := services.NewAuthService(db, cfg.JWT.Secret)
	dashboardService := services.NewDashboardService(db)
	budgetService := services.NewBudgetService(db)
	workflowService := services.NewWorkflowService(db)
	expenseService := services.NewExpenseService(db, budgetService, workflowService)

	// 设置服务间的依赖关系
	workflowService.SetExpenseService(expenseService)

	// 初始化控制器
	authController := controllers.NewAuthController(authService)
	dashboardController := controllers.NewDashboardController(dashboardService)
	expenseController := controllers.NewExpenseController(expenseService, budgetService)
	workflowController := controllers.NewWorkflowController(workflowService)

	// API路由组
	api := r.Group("/api/v1")
	{
		// 认证相关（无需认证）
		api.POST("/login", authController.Login)
		api.POST("/auth/refresh", authController.RefreshToken)

		// 需要认证的接口
		authorized := api.Group("")
		authorized.Use(middleware.JWTAuth(cfg.JWT.Secret))
		{
			// 认证相关
			authorized.POST("/auth/logout", authController.Logout)
			authorized.GET("/auth/profile", authController.GetProfile)
			authorized.POST("/auth/change-password", authController.ChangePassword)

			// 工作台相关
			authorized.GET("/dashboard/dept-head", dashboardController.GetDepartmentHeadDashboard)
			authorized.GET("/dashboard/pending-approvals", dashboardController.GetPendingApprovals)
			authorized.GET("/dashboard/budget-overview", dashboardController.GetBudgetOverview)

			// 支出控制相关
			authorized.POST("/expense-claims", expenseController.CreateClaim)
			authorized.GET("/expense-claims", expenseController.GetClaimList)
			authorized.GET("/expense-claims/:id", expenseController.GetClaim)
			authorized.DELETE("/expense-claims/:id", expenseController.DeleteClaim)
			authorized.POST("/expense-claims/:id/withdraw", expenseController.WithdrawClaim)

			// 预算相关
			authorized.GET("/budget-items/:budgetItemId/balance", expenseController.GetBudgetBalance)
			authorized.GET("/budget-items", expenseController.GetBudgetItems)

			// 费用类型相关
			authorized.GET("/expense-types", expenseController.GetExpenseTypes)

			// 搜索相关
			authorized.GET("/pre-approvals/search", expenseController.SearchPreApprovals)
			authorized.GET("/contracts/search", expenseController.SearchContracts)

			// 工作流相关
			authorized.POST("/approvals", workflowController.ProcessApproval)
			authorized.GET("/workflow/instance/:id", workflowController.GetWorkflowInstance)
			authorized.GET("/workflow/preview", workflowController.GetWorkflowPreview)
			authorized.POST("/workflow/instance/:id/terminate", workflowController.TerminateWorkflow)
			authorized.POST("/workflow/start", workflowController.StartWorkflow)
		}
	}

	return r
}
