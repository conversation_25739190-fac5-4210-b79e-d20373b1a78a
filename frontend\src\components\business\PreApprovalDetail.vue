<template>
  <div class="pre-approval-detail">
    <a-spin :spinning="loading">
      <!-- 基本信息 -->
      <a-descriptions title="事前申请基本信息" :column="2" bordered>
        <a-descriptions-item label="申请标题">{{ approvalData.title }}</a-descriptions-item>
        <a-descriptions-item label="申请编号">{{ approvalData.approvalNumber }}</a-descriptions-item>
        <a-descriptions-item label="申请人">{{ approvalData.applicantName }}</a-descriptions-item>
        <a-descriptions-item label="所属部门">{{ approvalData.departmentName }}</a-descriptions-item>
        <a-descriptions-item label="申请金额">
          <span style="color: #1890ff; font-weight: 500;">¥{{ approvalData.amount?.toFixed(2) || '0.00' }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="申请类型">
          <a-tag>{{ approvalData.approvalType }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="申请时间">{{ formatTime(approvalData.submittedAt) }}</a-descriptions-item>
        <a-descriptions-item label="申请状态">
          <a-tag :color="getStatusColor(approvalData.status)">
            {{ getStatusText(approvalData.status) }}
          </a-tag>
        </a-descriptions-item>
      </a-descriptions>

      <!-- 申请详情 -->
      <div style="margin-top: 16px;">
        <h3>申请详情</h3>
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="申请事由">
            <div style="white-space: pre-wrap;">{{ approvalData.reason }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="预计用途">
            <div style="white-space: pre-wrap;">{{ approvalData.purpose }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="预计时间">
            {{ approvalData.expectedStartDate }} 至 {{ approvalData.expectedEndDate }}
          </a-descriptions-item>
          <a-descriptions-item label="预算科目">{{ approvalData.budgetItemName }}</a-descriptions-item>
          <a-descriptions-item label="可用余额">
            <span :style="{ color: approvalData.availableBalance >= approvalData.amount ? '#52c41a' : '#ff4d4f' }">
              ¥{{ approvalData.availableBalance?.toFixed(2) || '0.00' }}
            </span>
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 费用明细 -->
      <div v-if="approvalData.details && approvalData.details.length > 0" style="margin-top: 16px;">
        <h3>费用明细</h3>
        <a-table
          :columns="detailColumns"
          :data-source="approvalData.details"
          :pagination="false"
          size="small"
          bordered
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'amount'">
              <span style="color: #1890ff;">¥{{ record.amount?.toFixed(2) || '0.00' }}</span>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 关联信息 -->
      <div v-if="approvalData.relatedContracts && approvalData.relatedContracts.length > 0" style="margin-top: 16px;">
        <h3>关联合同</h3>
        <a-list
          :data-source="approvalData.relatedContracts"
          size="small"
          bordered
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <template #actions>
                <a-button type="link" size="small" @click="viewContract(item)">查看合同</a-button>
              </template>
              <a-list-item-meta>
                <template #title>
                  <a @click="viewContract(item)">{{ item.contractTitle }}</a>
                </template>
                <template #description>
                  合同编号: {{ item.contractNumber }} | 金额: ¥{{ item.amount?.toFixed(2) }}
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </div>

      <!-- 附件信息 -->
      <div v-if="approvalData.attachments && approvalData.attachments.length > 0" style="margin-top: 16px;">
        <h3>申请附件</h3>
        <a-list
          :data-source="approvalData.attachments"
          size="small"
          bordered
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <template #actions>
                <a-button type="link" size="small" @click="downloadAttachment(item)">下载</a-button>
                <a-button type="link" size="small" @click="previewAttachment(item)">预览</a-button>
              </template>
              <a-list-item-meta>
                <template #title>
                  <a @click="previewAttachment(item)">{{ item.fileName }}</a>
                </template>
                <template #description>
                  {{ formatFileSize(item.fileSize) }} | {{ formatTime(item.uploadTime) }}
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </div>

      <!-- 审批流程 -->
      <div style="margin-top: 16px;">
        <h3>审批流程</h3>
        <WorkflowChart
          :business-type="'pre_approval'"
          :business-amount="approvalData.amount"
          :department-id="approvalData.departmentId"
          :show-preview="true"
          :show-history="false"
          title=""
        />
      </div>
    </a-spin>

    <!-- 附件预览弹窗 -->
    <a-modal
      v-model:visible="attachmentModalVisible"
      :title="currentAttachment?.fileName"
      width="800px"
      :footer="null"
    >
      <div v-if="currentAttachment" style="text-align: center;">
        <img 
          v-if="isImageFile(currentAttachment.fileName)"
          :src="currentAttachment.previewUrl" 
          style="max-width: 100%; max-height: 500px;"
          alt="附件预览"
        />
        <div v-else style="padding: 40px; color: #666;">
          <FileOutlined style="font-size: 48px; margin-bottom: 16px;" />
          <div>{{ currentAttachment.fileName }}</div>
          <div style="margin-top: 8px;">
            <a-button type="primary" @click="downloadAttachment(currentAttachment)">下载文件</a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { FileOutlined } from '@ant-design/icons-vue'
import WorkflowChart from '@/components/WorkflowChart.vue'

// Props
interface Props {
  businessId: string
  businessType: string
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: true
})

// 响应式数据
const loading = ref(false)
const attachmentModalVisible = ref(false)
const approvalData = ref<any>({})
const currentAttachment = ref<any>(null)

// 表格列定义
const detailColumns = [
  { title: '费用项目', dataIndex: 'itemName', key: 'itemName', width: 150 },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 100 },
  { title: '单价', dataIndex: 'unitPrice', key: 'unitPrice', width: 120 },
  { title: '金额', key: 'amount', width: 120 },
  { title: '说明', dataIndex: 'description', key: 'description', ellipsis: true }
]

// 格式化时间
const formatTime = (time: string) => {
  return time ? new Date(time).toLocaleString() : '-'
}

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) return `${size}B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
  return `${(size / (1024 * 1024)).toFixed(1)}MB`
}

// 判断是否为图片文件
const isImageFile = (fileName: string) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return imageExtensions.includes(ext)
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    draft: 'default',
    pending: 'processing',
    approved: 'success',
    rejected: 'error',
    used: 'blue'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    draft: '草稿',
    pending: '审批中',
    approved: '已批准',
    rejected: '已驳回',
    used: '已使用'
  }
  return textMap[status] || status
}

// 加载事前申请详情
const loadApprovalDetail = async () => {
  loading.value = true
  try {
    // 模拟数据，实际应该调用API
    approvalData.value = {
      title: '办公设备采购事前申请',
      approvalNumber: 'PA2024001',
      applicantName: '张三',
      departmentName: '行政科',
      amount: 15000.00,
      approvalType: '设备采购',
      submittedAt: new Date().toISOString(),
      status: 'pending',
      reason: '部门现有办公设备老化，需要更新换代以提高工作效率',
      purpose: '采购办公电脑、打印机等设备',
      expectedStartDate: '2024-02-01',
      expectedEndDate: '2024-02-28',
      budgetItemName: '办公设备费',
      availableBalance: 50000.00,
      departmentId: 'dept1',
      details: [
        {
          itemName: '办公电脑',
          quantity: 3,
          unitPrice: 4000.00,
          amount: 12000.00,
          description: '台式机，配置要求：i5处理器，8G内存，256G固态硬盘'
        },
        {
          itemName: '激光打印机',
          quantity: 1,
          unitPrice: 3000.00,
          amount: 3000.00,
          description: '黑白激光打印机，支持双面打印'
        }
      ],
      attachments: [
        {
          fileName: '设备采购清单.xlsx',
          fileSize: 512000,
          uploadTime: new Date().toISOString(),
          previewUrl: ''
        }
      ]
    }
  } catch (error) {
    console.error('加载事前申请详情失败:', error)
    message.error('加载事前申请详情失败')
  } finally {
    loading.value = false
  }
}

// 查看合同
const viewContract = (contract: any) => {
  message.info('查看合同功能开发中...')
}

// 下载附件
const downloadAttachment = (attachment: any) => {
  message.info('下载附件功能开发中...')
}

// 预览附件
const previewAttachment = (attachment: any) => {
  currentAttachment.value = attachment
  attachmentModalVisible.value = true
}

// 初始化
onMounted(() => {
  if (props.businessId) {
    loadApprovalDetail()
  }
})
</script>

<style scoped>
.pre-approval-detail {
  padding: 16px;
}

.pre-approval-detail h3 {
  margin-bottom: 16px;
  color: #262626;
  font-weight: 500;
}
</style>
