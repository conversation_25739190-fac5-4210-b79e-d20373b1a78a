package database

import (
	"log"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"hospital-management/internal/models"
)

// SeedData 初始化基础数据
func SeedData(db *gorm.DB) error {
	log.Println("开始初始化基础数据...")

	// 创建费用类型
	if err := seedExpenseTypes(db); err != nil {
		return err
	}

	// 创建工作流定义
	if err := seedWorkflowDefinitions(db); err != nil {
		return err
	}

	// 创建示例预算项
	if err := seedBudgetItems(db); err != nil {
		return err
	}

	log.Println("基础数据初始化完成")
	return nil
}

// seedExpenseTypes 创建费用类型
func seedExpenseTypes(db *gorm.DB) error {
	expenseTypes := []models.ExpenseType{
		{
			BaseModel: models.BaseModel{ID: uuid.New()},
			Name:      "办公费",
			Code:      "OFFICE",
			Description: "办公用品、文具、纸张等日常办公费用",
			Status:    "active",
		},
		{
			BaseModel: models.BaseModel{ID: uuid.New()},
			Name:      "差旅费",
			Code:      "TRAVEL",
			Description: "出差交通费、住宿费、餐费等",
			Status:    "active",
		},
		{
			BaseModel: models.BaseModel{ID: uuid.New()},
			Name:      "招待费",
			Code:      "ENTERTAINMENT",
			Description: "业务招待、会议费用等",
			Status:    "active",
		},
		{
			BaseModel: models.BaseModel{ID: uuid.New()},
			Name:      "培训费",
			Code:      "TRAINING",
			Description: "员工培训、学习费用",
			Status:    "active",
		},
		{
			BaseModel: models.BaseModel{ID: uuid.New()},
			Name:      "设备费",
			Code:      "EQUIPMENT",
			Description: "设备采购、维修费用",
			Status:    "active",
		},
		{
			BaseModel: models.BaseModel{ID: uuid.New()},
			Name:      "医疗费",
			Code:      "MEDICAL",
			Description: "医疗用品、药品采购费用",
			Status:    "active",
		},
	}

	for _, expenseType := range expenseTypes {
		var existing models.ExpenseType
		if err := db.Where("code = ?", expenseType.Code).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := db.Create(&expenseType).Error; err != nil {
					log.Printf("创建费用类型失败 %s: %v", expenseType.Name, err)
					return err
				}
				log.Printf("✓ 已创建费用类型: %s", expenseType.Name)
			} else {
				return err
			}
		} else {
			log.Printf("- 费用类型已存在: %s", expenseType.Name)
		}
	}

	return nil
}

// seedWorkflowDefinitions 创建工作流定义
func seedWorkflowDefinitions(db *gorm.DB) error {
	// 报销申请审批流程
	expenseClaimDefinition := models.WorkflowDefinition{
		BaseModel: models.BaseModel{ID: uuid.New()},
		Key:       "expense_claim_approval",
		Name:      "报销申请审批流程",
		Description: "通用报销申请的审批流程",
		Version:   1,
		Status:    "active",
	}

	var existing models.WorkflowDefinition
	if err := db.Where("key = ?", expenseClaimDefinition.Key).First(&existing).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			if err := db.Create(&expenseClaimDefinition).Error; err != nil {
				log.Printf("创建工作流定义失败: %v", err)
				return err
			}
			log.Printf("✓ 已创建工作流定义: %s", expenseClaimDefinition.Name)

			// 创建工作流节点
			nodes := []models.WorkflowNode{
				{
					BaseModel:    models.BaseModel{ID: uuid.New()},
					DefinitionID: expenseClaimDefinition.ID,
					NodeID:       "start",
					NodeType:     "start",
					NodeName:     "发起申请",
					NextNodes:    "dept_head",
					Assignee:     "applicant",
				},
				{
					BaseModel:    models.BaseModel{ID: uuid.New()},
					DefinitionID: expenseClaimDefinition.ID,
					NodeID:       "dept_head",
					NodeType:     "approval",
					NodeName:     "部门负责人审批",
					NextNodes:    "finance",
					Assignee:     "department_head",
				},
				{
					BaseModel:    models.BaseModel{ID: uuid.New()},
					DefinitionID: expenseClaimDefinition.ID,
					NodeID:       "finance",
					NodeType:     "approval",
					NodeName:     "财务审核",
					NextNodes:    "end",
					Assignee:     "finance_dept",
				},
				{
					BaseModel:    models.BaseModel{ID: uuid.New()},
					DefinitionID: expenseClaimDefinition.ID,
					NodeID:       "end",
					NodeType:     "end",
					NodeName:     "审批完成",
					NextNodes:    "",
					Assignee:     "",
				},
			}

			for _, node := range nodes {
				if err := db.Create(&node).Error; err != nil {
					log.Printf("创建工作流节点失败 %s: %v", node.NodeName, err)
					return err
				}
				log.Printf("✓ 已创建工作流节点: %s", node.NodeName)
			}
		} else {
			return err
		}
	} else {
		log.Printf("- 工作流定义已存在: %s", expenseClaimDefinition.Name)
	}

	return nil
}

// seedBudgetItems 创建示例预算项
func seedBudgetItems(db *gorm.DB) error {
	// 获取部门信息
	var departments []models.Department
	if err := db.Find(&departments).Error; err != nil {
		log.Printf("获取部门信息失败: %v", err)
		return err
	}

	if len(departments) == 0 {
		log.Println("- 没有部门信息，跳过预算项创建")
		return nil
	}

	currentYear := time.Now().Year()

	// 为每个部门创建基础预算项
	budgetSubjects := []struct {
		Code   string
		Name   string
		Amount float64
	}{
		{"1001", "办公费", 50000.00},
		{"1002", "差旅费", 30000.00},
		{"1003", "招待费", 20000.00},
		{"1004", "培训费", 15000.00},
		{"1005", "设备费", 100000.00},
		{"1006", "医疗费", 200000.00},
	}

	for _, dept := range departments {
		for _, subject := range budgetSubjects {
			budgetItem := models.BudgetItem{
				BaseModel:    models.BaseModel{ID: uuid.New()},
				Year:         currentYear,
				DepartmentID: dept.ID,
				SubjectCode:  subject.Code,
				SubjectName:  subject.Name,
				TotalAmount:  subject.Amount,
				UsedAmount:   0,
				FrozenAmount: 0,
				ControlType:  "rigid",
				Status:       "active",
			}

			var existing models.BudgetItem
			if err := db.Where("year = ? AND department_id = ? AND subject_code = ?", 
				currentYear, dept.ID, subject.Code).First(&existing).Error; err != nil {
				if err == gorm.ErrRecordNotFound {
					if err := db.Create(&budgetItem).Error; err != nil {
						log.Printf("创建预算项失败 %s-%s: %v", dept.Name, subject.Name, err)
						return err
					}
					log.Printf("✓ 已创建预算项: %s-%s", dept.Name, subject.Name)
				} else {
					return err
				}
			} else {
				log.Printf("- 预算项已存在: %s-%s", dept.Name, subject.Name)
			}
		}
	}

	return nil
}
