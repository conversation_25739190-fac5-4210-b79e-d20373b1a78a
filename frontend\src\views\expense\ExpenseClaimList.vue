<template>
  <div class="expense-claim-list">
    <div class="page-header">
      <h2>报销申请管理</h2>
      <a-button type="primary" @click="showCreateModal">
        <PlusOutlined />
        新建报销申请
      </a-button>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-form">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="状态">
          <a-select v-model:value="searchForm.status" placeholder="请选择状态" style="width: 120px" allowClear>
            <a-select-option value="draft">草稿</a-select-option>
            <a-select-option value="pending">待审批</a-select-option>
            <a-select-option value="approved">已通过</a-select-option>
            <a-select-option value="rejected">已驳回</a-select-option>
            <a-select-option value="paid">已支付</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="关键词">
          <a-input v-model:value="searchForm.keyword" placeholder="请输入标题关键词" style="width: 200px" />
        </a-form-item>
        <a-form-item label="申请时间">
          <a-range-picker v-model:value="searchForm.dateRange" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">搜索</a-button>
          <a-button @click="handleReset" style="margin-left: 8px">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 数据表格 -->
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
    >
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">
          {{ getStatusText(record.status) }}
        </a-tag>
      </template>
      
      <template #totalAmount="{ record }">
        ¥{{ record.totalAmount.toFixed(2) }}
      </template>
      
      <template #action="{ record }">
        <a-space>
          <a-button type="link" size="small" @click="viewDetail(record)">查看</a-button>
          <a-button v-if="record.status === 'draft'" type="link" size="small" @click="editClaim(record)">编辑</a-button>
          <a-button v-if="record.status === 'pending'" type="link" size="small" @click="withdrawClaim(record)">撤回</a-button>
          <a-button v-if="record.status === 'draft'" type="link" size="small" danger @click="deleteClaim(record)">删除</a-button>
        </a-space>
      </template>
    </a-table>

    <!-- 创建/编辑弹窗 -->
    <ExpenseClaimModal
      v-model:visible="modalVisible"
      :claim-data="currentClaim"
      @success="handleModalSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import { expenseApi } from '@/api/expense'
import ExpenseClaimModal from './components/ExpenseClaimModal.vue'
import type { ExpenseClaim } from '@/types/expense'

// 响应式数据
const loading = ref(false)
const modalVisible = ref(false)
const dataSource = ref<ExpenseClaim[]>([])
const currentClaim = ref<ExpenseClaim | null>(null)

// 搜索表单
const searchForm = reactive({
  status: undefined,
  keyword: '',
  dateRange: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '申请编号',
    dataIndex: 'id',
    key: 'id',
    width: 120,
    ellipsis: true
  },
  {
    title: '申请事由',
    dataIndex: 'title',
    key: 'title',
    ellipsis: true
  },
  {
    title: '申请人',
    dataIndex: 'applicantName',
    key: 'applicantName',
    width: 100
  },
  {
    title: '部门',
    dataIndex: 'departmentName',
    key: 'departmentName',
    width: 100
  },
  {
    title: '金额',
    dataIndex: 'totalAmount',
    key: 'totalAmount',
    width: 120,
    slots: { customRender: 'totalAmount' }
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    slots: { customRender: 'status' }
  },
  {
    title: '申请时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    slots: { customRender: 'action' }
  }
]

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    draft: 'default',
    pending: 'processing',
    approved: 'success',
    rejected: 'error',
    paid: 'success'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    draft: '草稿',
    pending: '待审批',
    approved: '已通过',
    rejected: '已驳回',
    paid: '已支付'
  }
  return textMap[status] || status
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      status: searchForm.status,
      keyword: searchForm.keyword,
      startDate: searchForm.dateRange?.[0]?.format('YYYY-MM-DD'),
      endDate: searchForm.dateRange?.[1]?.format('YYYY-MM-DD')
    }
    
    const response = await expenseApi.getClaimList(params)
    dataSource.value = response.items || []
    pagination.total = response.total || 0
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    status: undefined,
    keyword: '',
    dateRange: undefined
  })
  pagination.current = 1
  loadData()
}

// 表格变化
const handleTableChange: TableProps['onChange'] = (pag) => {
  pagination.current = pag.current || 1
  pagination.pageSize = pag.pageSize || 10
  loadData()
}

// 显示创建弹窗
const showCreateModal = () => {
  currentClaim.value = null
  modalVisible.value = true
}

// 查看详情
const viewDetail = (record: ExpenseClaim) => {
  // TODO: 跳转到详情页面
  console.log('查看详情:', record)
}

// 编辑
const editClaim = (record: ExpenseClaim) => {
  currentClaim.value = record
  modalVisible.value = true
}

// 撤回
const withdrawClaim = async (record: ExpenseClaim) => {
  try {
    await expenseApi.withdrawClaim(record.id!)
    message.success('撤回成功')
    loadData()
  } catch (error) {
    console.error('撤回失败:', error)
  }
}

// 删除
const deleteClaim = async (record: ExpenseClaim) => {
  try {
    await expenseApi.deleteClaim(record.id!)
    message.success('删除成功')
    loadData()
  } catch (error) {
    console.error('删除失败:', error)
  }
}

// 弹窗成功回调
const handleModalSuccess = () => {
  modalVisible.value = false
  loadData()
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.expense-claim-list {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.search-form {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}
</style>
