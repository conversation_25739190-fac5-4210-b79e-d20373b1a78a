<template>
  <a-layout class="layout">
    <!-- 移动端遮罩层 -->
    <div v-if="isMobile && !collapsed" class="mobile-mask" @click="collapsed = true"></div>

    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      :class="['sider', { 'sider-mobile': isMobile, 'sider-mobile-open': isMobile && !collapsed }]"
      :collapsed-width="isMobile ? 0 : 80">
      <div class="logo">
        <h3 v-if="!collapsed">寻甸县第一人民医院</h3>
        <h3 v-else>XDYY</h3>
      </div>

      <a-menu v-model:selectedKeys="selectedKeys" v-model:openKeys="openKeys" mode="inline" theme="dark"
        :items="menuItems" @click="handleMenuClick" />
    </a-layout-sider>

    <!-- 主内容区 -->
    <a-layout :class="['main-layout', { 'main-layout-collapsed': collapsed }]">
      <!-- 顶部导航 -->
      <a-layout-header class="header">
        <div class="header-left">
          <a-button type="text" :icon="collapsed ? h(MenuUnfoldOutlined) : h(MenuFoldOutlined)"
            @click="() => (collapsed = !collapsed)" class="trigger" />

          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item v-for="item in breadcrumbItems" :key="item.path">
              {{ item.title }}
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>

        <div class="header-right">
          <a-badge :count="unreadCount" class="notification">
            <a-button type="text" :icon="h(BellOutlined)" @click="handleNotificationClick" />
          </a-badge>

          <a-dropdown>
            <a-button type="text" class="user-info">
              <a-avatar size="small" :icon="h(UserOutlined)" />
              <span class="username">{{ userStore.userName || '用户' }} ({{ userStore.userDepartment }})</span>
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item key="profile">
                  <UserOutlined />
                  个人信息
                </a-menu-item>
                <a-menu-item key="settings">
                  <SettingOutlined />
                  系统设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>

      <!-- 内容区域 -->
      <a-layout-content class="content">
        <router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, h, watch, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
  UserOutlined,
  DownOutlined,
  SettingOutlined,
  LogoutOutlined,
  DashboardOutlined,
  TeamOutlined,
  DollarOutlined,
  ShoppingOutlined,
  FileTextOutlined,
  DatabaseOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const collapsed = ref(false)
const selectedKeys = ref<string[]>([])
const openKeys = ref<string[]>([])
const isMobile = ref(false)
const unreadCount = ref(0)

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
  if (isMobile.value) {
    collapsed.value = true
  }
}

// 监听窗口大小变化
const handleResize = () => {
  checkMobile()
}

// 获取未读消息数量
const fetchUnreadCount = async () => {
  try {
    // 这里应该调用API获取未读消息数量
    // const response = await getUnreadMessageCount()
    // unreadCount.value = response.data.count
    
    // 模拟数据
    unreadCount.value = 5
  } catch (error) {
    console.error('获取未读消息数量失败:', error)
  }
}

// 处理消息通知点击
const handleNotificationClick = () => {
  router.push('/notifications')
}

// 初始化
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', handleResize)
  fetchUnreadCount()
})

// 清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 根据用户角色动态生成菜单
const menuItems = computed(() => {
  const roles = userStore.userRoles || []
  const isAdmin = roles.includes('admin')
  const isDeptHead = roles.includes('dept_head')
  const isFinance = roles.includes('finance')
  
  const baseMenus = [
    {
      key: '/',
      icon: h(DashboardOutlined),
      label: isDeptHead ? '部门负责人工作台' : '工作台',
      title: isDeptHead ? '部门负责人工作台' : '工作台'
    }
  ]
  
  // 系统管理菜单（仅管理员可见）
  if (isAdmin) {
    baseMenus.push({
      key: 'system',
      icon: h(TeamOutlined),
      label: '系统管理',
      title: '系统管理',
      children: [
        {
          key: '/system/departments',
          label: '部门管理',
          title: '部门管理'
        },
        {
          key: '/system/users',
          label: '用户管理',
          title: '用户管理'
        },
        {
          key: '/system/roles',
          label: '角色管理',
          title: '角色管理'
        }
      ]
    })
  }
  
  // 预算管理菜单（管理员和部门负责人可见）
  if (isAdmin || isDeptHead) {
    baseMenus.push({
      key: 'budget',
      icon: h(DollarOutlined),
      label: '预算管理',
      title: '预算管理',
      children: [
        {
          key: '/budget/schemes',
          label: '预算方案',
          title: '预算方案'
        },
        {
          key: '/budget/subjects',
          label: '预算科目',
          title: '预算科目'
        },
        {
          key: '/budget/items',
          label: '预算明细',
          title: '预算明细'
        }
      ]
    })
  }
  
  // 支出控制菜单（所有人可见）
  baseMenus.push({
    key: 'expense',
    icon: h(DollarOutlined),
    label: '支出控制',
    title: '支出控制',
    children: [
      {
        key: '/expense/claims',
        label: '报销申请管理',
        title: '报销申请管理'
      }
    ]
  })

  // 审批管理菜单（有审批权限的用户可见）
  if (isDeptHead || isFinance || roles.includes('approver')) {
    baseMenus.push({
      key: 'approval',
      icon: h(FileTextOutlined),
      label: '审批管理',
      title: '审批管理',
      children: [
        {
          key: '/approval/tasks',
          label: '待办审批',
          title: '待办审批'
        },
        {
          key: '/approval/history',
          label: '审批历史',
          title: '审批历史'
        }
      ]
    })
  }

  // 工作流管理菜单（管理员和部门负责人可见）
  if (isAdmin || isDeptHead) {
    baseMenus.push({
      key: 'workflow',
      icon: h(FileTextOutlined),
      label: '工作流管理',
      title: '工作流管理',
      children: [
        {
          key: '/workflow/management',
          label: '流程管理',
          title: '流程管理'
        },
        {
          key: '/workflow/monitor',
          label: '流程监控',
          title: '流程监控'
        }
      ]
    })
  }
  
  // 采购管理菜单
  baseMenus.push({
    key: 'procurement',
    icon: h(ShoppingOutlined),
    label: '采购管理',
    title: '采购管理',
    children: [
      {
        key: '/procurement/suppliers',
        label: '供应商管理',
        title: '供应商管理'
      },
      {
        key: '/procurement/requisitions',
        label: '采购申请',
        title: '采购申请'
      }
    ]
  })
  
  // 合同管理菜单
  baseMenus.push({
    key: 'contract',
    icon: h(FileTextOutlined),
    label: '合同管理',
    title: '合同管理',
    children: [
      {
        key: '/contract/contracts',
        label: '合同台账',
        title: '合同台账'
      },
      {
        key: '/contract/payment-schedules',
        label: '付款计划',
        title: '付款计划'
      }
    ]
  })
  
  // 资产管理菜单
  baseMenus.push({
    key: 'asset',
    icon: h(DatabaseOutlined),
    label: '资产管理',
    title: '资产管理',
    children: [
      {
        key: '/asset/categories',
        label: '资产分类',
        title: '资产分类'
      },
      {
        key: '/asset/assets',
        label: '资产台账',
        title: '资产台账'
      }
    ]
  })
  
  return baseMenus
})

// 面包屑导航
const breadcrumbItems = computed(() => {
  const items = []
  const pathSegments = route.path.split('/').filter(Boolean)

  // 查找当前路径对应的菜单项
  const findMenuItem = (items: any[], path: string): any => {
    for (const item of items) {
      if (item.key === path) {
        return item
      }
      if (item.children) {
        const found = findMenuItem(item.children, path)
        if (found) {
          return found
        }
      }
    }
    return null
  }

  const currentItem = findMenuItem(menuItems.value, route.path)
  if (currentItem) {
    // 查找父级菜单
    const findParent = (items: any[], targetKey: string): any => {
      for (const item of items) {
        if (item.children) {
          const found = item.children.find((child: any) => child.key === targetKey)
          if (found) {
            return item
          }
        }
      }
      return null
    }

    const parent = findParent(menuItems.value, route.path)
    if (parent) {
      items.push({ title: parent.title, path: parent.key })
    }
    items.push({ title: currentItem.title, path: currentItem.key })
  }

  return items
})

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  if (key.startsWith('/')) {
    router.push(key)
  }
}

// 处理退出登录
const handleLogout = async () => {
  await userStore.logoutAction()
  router.push('/login')
}

// 监听路由变化，更新选中的菜单
watch(
  () => route.path,
  (newPath) => {
    selectedKeys.value = [newPath]

    // 设置展开的菜单
    const pathSegments = newPath.split('/').filter(Boolean)
    if (pathSegments.length > 1) {
      openKeys.value = [pathSegments[0]]
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.layout {
  min-height: 100vh;
}

.sider {
  overflow: auto;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
}

.main-layout {
  margin-left: 200px;
  transition: margin-left 0.2s;
}

.main-layout-collapsed {
  margin-left: 80px;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 6px;
}

.logo h3 {
  color: white;
  margin: 0;
  font-size: 16px;
}

.header {
  background: white;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  height: 64px;
  width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
}

.trigger {
  font-size: 18px;
  height: 64px;
  width: 64px;
  cursor: pointer;
  transition: color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.trigger:hover {
  color: #1890ff;
}

.breadcrumb {
  margin-left: 8px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.username {
  margin-left: 8px;
}

.content {
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

/* 移动端遮罩层 */
.mobile-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.45);
  z-index: 999;
}

/* 移动端侧边栏 */
.sider-mobile {
  position: fixed !important;
  z-index: 1000;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.sider-mobile-open {
  transform: translateX(0) !important;
}

/* 响应式处理 */
@media (max-width: 768px) {
  .main-layout {
    margin-left: 0 !important;
  }

  .main-layout-collapsed {
    margin-left: 0 !important;
  }

  .header {
    padding: 0 16px !important;
  }

  .content {
    padding: 16px !important;
  }
}
</style>