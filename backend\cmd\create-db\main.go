package main

import (
	"fmt"
	"log"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"hospital-management/internal/config"
	"hospital-management/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Setup("debug", "")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("加载配置失败:", err)
	}

	// 连接到默认的postgres数据库
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=postgres sslmode=%s",
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.SSLMode,
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	// 创建hospital_management数据库
	dbName := "hospital_management"

	// 检查数据库是否存在
	var exists bool
	err = db.Raw("SELECT EXISTS(SELECT datname FROM pg_catalog.pg_database WHERE datname = ?)", dbName).Scan(&exists).Error
	if err != nil {
		log.Fatal("检查数据库是否存在失败:", err)
	}

	if exists {
		log.Printf("数据库 %s 已存在，先删除...", dbName)
		// 终止所有连接到该数据库的会话
		err = db.Exec("SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = ? AND pid <> pg_backend_pid()", dbName).Error
		if err != nil {
			log.Printf("终止数据库连接失败: %v", err)
		}

		// 删除数据库
		err = db.Exec(fmt.Sprintf("DROP DATABASE IF EXISTS %s", dbName)).Error
		if err != nil {
			log.Fatal("删除数据库失败:", err)
		}
		log.Printf("✓ 已删除数据库: %s", dbName)
	}

	// 创建新数据库
	err = db.Exec(fmt.Sprintf("CREATE DATABASE %s", dbName)).Error
	if err != nil {
		log.Fatal("创建数据库失败:", err)
	}

	log.Printf("✓ 已创建数据库: %s", dbName)
	log.Println("数据库创建完成！")
}
