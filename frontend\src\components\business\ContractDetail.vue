<template>
  <div class="contract-detail">
    <a-spin :spinning="loading">
      <!-- 基本信息 -->
      <a-descriptions title="合同基本信息" :column="2" bordered>
        <a-descriptions-item label="合同标题">{{ contractData.title }}</a-descriptions-item>
        <a-descriptions-item label="合同编号">{{ contractData.contractNumber }}</a-descriptions-item>
        <a-descriptions-item label="申请人">{{ contractData.applicantName }}</a-descriptions-item>
        <a-descriptions-item label="所属部门">{{ contractData.departmentName }}</a-descriptions-item>
        <a-descriptions-item label="合同金额">
          <span style="color: #1890ff; font-weight: 500;">¥{{ contractData.amount?.toFixed(2) || '0.00' }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="合同类型">
          <a-tag>{{ contractData.contractType }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="申请时间">{{ formatTime(contractData.submittedAt) }}</a-descriptions-item>
        <a-descriptions-item label="合同状态">
          <a-tag :color="getStatusColor(contractData.status)">
            {{ getStatusText(contractData.status) }}
          </a-tag>
        </a-descriptions-item>
      </a-descriptions>

      <!-- 合同方信息 -->
      <a-descriptions title="合同方信息" :column="2" bordered style="margin-top: 16px;">
        <a-descriptions-item label="甲方（我方）">{{ contractData.partyA || '寻甸县第一人民医院' }}</a-descriptions-item>
        <a-descriptions-item label="乙方">{{ contractData.partyB }}</a-descriptions-item>
        <a-descriptions-item label="乙方联系人">{{ contractData.partyBContact }}</a-descriptions-item>
        <a-descriptions-item label="联系电话">{{ contractData.partyBPhone }}</a-descriptions-item>
        <a-descriptions-item label="乙方地址" :span="2">{{ contractData.partyBAddress }}</a-descriptions-item>
      </a-descriptions>

      <!-- 合同条款 -->
      <div style="margin-top: 16px;">
        <h3>合同主要条款</h3>
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="合同内容">
            <div style="white-space: pre-wrap;">{{ contractData.content }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="履行期限">
            {{ contractData.startDate }} 至 {{ contractData.endDate }}
          </a-descriptions-item>
          <a-descriptions-item label="付款方式">{{ contractData.paymentMethod }}</a-descriptions-item>
          <a-descriptions-item label="违约责任">
            <div style="white-space: pre-wrap;">{{ contractData.breachClause }}</div>
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 附件信息 -->
      <div v-if="contractData.attachments && contractData.attachments.length > 0" style="margin-top: 16px;">
        <h3>合同附件</h3>
        <a-list
          :data-source="contractData.attachments"
          size="small"
          bordered
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <template #actions>
                <a-button type="link" size="small" @click="downloadAttachment(item)">下载</a-button>
                <a-button type="link" size="small" @click="previewAttachment(item)">预览</a-button>
              </template>
              <a-list-item-meta>
                <template #title>
                  <a @click="previewAttachment(item)">{{ item.fileName }}</a>
                </template>
                <template #description>
                  {{ formatFileSize(item.fileSize) }} | {{ formatTime(item.uploadTime) }}
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </div>

      <!-- 审批流程 -->
      <div style="margin-top: 16px;">
        <h3>审批流程</h3>
        <WorkflowChart
          :business-type="'contract'"
          :business-amount="contractData.amount"
          :department-id="contractData.departmentId"
          :show-preview="true"
          :show-history="false"
          title=""
        />
      </div>
    </a-spin>

    <!-- 附件预览弹窗 -->
    <a-modal
      v-model:visible="attachmentModalVisible"
      :title="currentAttachment?.fileName"
      width="800px"
      :footer="null"
    >
      <div v-if="currentAttachment" style="text-align: center;">
        <img 
          v-if="isImageFile(currentAttachment.fileName)"
          :src="currentAttachment.previewUrl" 
          style="max-width: 100%; max-height: 500px;"
          alt="附件预览"
        />
        <div v-else style="padding: 40px; color: #666;">
          <FileOutlined style="font-size: 48px; margin-bottom: 16px;" />
          <div>{{ currentAttachment.fileName }}</div>
          <div style="margin-top: 8px;">
            <a-button type="primary" @click="downloadAttachment(currentAttachment)">下载文件</a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { FileOutlined } from '@ant-design/icons-vue'
import WorkflowChart from '@/components/WorkflowChart.vue'

// Props
interface Props {
  businessId: string
  businessType: string
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: true
})

// 响应式数据
const loading = ref(false)
const attachmentModalVisible = ref(false)
const contractData = ref<any>({})
const currentAttachment = ref<any>(null)

// 格式化时间
const formatTime = (time: string) => {
  return time ? new Date(time).toLocaleString() : '-'
}

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) return `${size}B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
  return `${(size / (1024 * 1024)).toFixed(1)}MB`
}

// 判断是否为图片文件
const isImageFile = (fileName: string) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return imageExtensions.includes(ext)
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    draft: 'default',
    pending: 'processing',
    approved: 'success',
    rejected: 'error',
    signed: 'green',
    executed: 'blue'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    draft: '草稿',
    pending: '审批中',
    approved: '已批准',
    rejected: '已驳回',
    signed: '已签署',
    executed: '执行中'
  }
  return textMap[status] || status
}

// 加载合同详情
const loadContractDetail = async () => {
  loading.value = true
  try {
    // 模拟数据，实际应该调用API
    contractData.value = {
      title: '医疗设备采购合同',
      contractNumber: 'HT2024001',
      applicantName: '张三',
      departmentName: '设备科',
      amount: 500000.00,
      contractType: '采购合同',
      submittedAt: new Date().toISOString(),
      status: 'pending',
      partyA: '寻甸县第一人民医院',
      partyB: '某医疗设备有限公司',
      partyBContact: '李经理',
      partyBPhone: '13800138000',
      partyBAddress: '北京市朝阳区某某路123号',
      content: '采购CT设备一台，包含安装调试及培训服务',
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      paymentMethod: '分期付款',
      breachClause: '违约方应承担相应的违约责任',
      departmentId: 'dept1',
      attachments: [
        {
          fileName: '合同文本.pdf',
          fileSize: 1024000,
          uploadTime: new Date().toISOString(),
          previewUrl: ''
        }
      ]
    }
  } catch (error) {
    console.error('加载合同详情失败:', error)
    message.error('加载合同详情失败')
  } finally {
    loading.value = false
  }
}

// 下载附件
const downloadAttachment = (attachment: any) => {
  message.info('下载附件功能开发中...')
}

// 预览附件
const previewAttachment = (attachment: any) => {
  currentAttachment.value = attachment
  attachmentModalVisible.value = true
}

// 初始化
onMounted(() => {
  if (props.businessId) {
    loadContractDetail()
  }
})
</script>

<style scoped>
.contract-detail {
  padding: 16px;
}

.contract-detail h3 {
  margin-bottom: 16px;
  color: #262626;
  font-weight: 500;
}
</style>
