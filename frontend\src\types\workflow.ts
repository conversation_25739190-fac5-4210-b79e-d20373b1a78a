// 工作流相关类型定义

export interface WorkflowNode {
  id: string
  label: string
  status: 'pending' | 'processing' | 'completed' | 'rejected'
  assignee: string
}

export interface WorkflowEdge {
  source: string
  target: string
}

export interface WorkflowInstance {
  id: string
  definitionKey: string
  businessId: string
  businessType: string
  status: 'active' | 'completed' | 'terminated' | 'rejected'
  startTime: string
  endTime?: string
  nodes: WorkflowNode[]
  edges: WorkflowEdge[]
}

export interface WorkflowPreview {
  nodes: WorkflowNode[]
  edges: WorkflowEdge[]
}

export interface ApprovalHistory {
  id: string
  instanceId: string
  nodeId: string
  nodeName: string
  approverId: string
  approverName: string
  action: 'approve' | 'reject' | 'start'
  comment: string
  createdAt: string
}

export interface ApprovalTask {
  id: string
  type: string
  title: string
  applicant: string
  applicantName: string
  summary: string
  amount?: number
  submissionTime: string
  urgentLevel?: 'normal' | 'urgent' | 'emergency'
  businessId: string
  businessType: string
  workflowInstanceId: string
  currentNodeId: string
  link: string
}

// 审批动作类型
export type ApprovalAction = 'approve' | 'reject'

// 审批表单数据
export interface ApprovalFormData {
  instanceId: string
  action: ApprovalAction
  comment?: string
}

// 工作流启动请求
export interface StartWorkflowRequest {
  definitionKey: string
  businessId: string
  businessType: string
  startUserId: string
  variables?: Record<string, any>
}

// 工作流启动响应
export interface StartWorkflowResponse {
  instanceId: string
  currentNodeId: string
  status: string
}

// 终止工作流请求
export interface TerminateWorkflowRequest {
  reason: string
}

// 工作流定义
export interface WorkflowDefinition {
  id: string
  key: string
  name: string
  description: string
  version: number
  status: 'active' | 'inactive'
  nodes: WorkflowNodeDefinition[]
}

export interface WorkflowNodeDefinition {
  id: string
  definitionId: string
  nodeId: string
  nodeType: 'start' | 'end' | 'approval' | 'gateway'
  nodeName: string
  nextNodes: string
  assignee: string
  conditions?: string
}

// 审批状态选项
export const APPROVAL_STATUS_OPTIONS = [
  { label: '待处理', value: 'pending', color: 'processing' },
  { label: '处理中', value: 'processing', color: 'warning' },
  { label: '已完成', value: 'completed', color: 'success' },
  { label: '已驳回', value: 'rejected', color: 'error' },
  { label: '已终止', value: 'terminated', color: 'default' }
]

// 紧急程度选项
export const URGENT_LEVEL_OPTIONS = [
  { label: '普通', value: 'normal', color: 'default' },
  { label: '紧急', value: 'urgent', color: 'warning' },
  { label: '特急', value: 'emergency', color: 'error' }
]

// 业务类型选项
export const BUSINESS_TYPE_OPTIONS = [
  { label: '报销申请', value: 'expense_claim' },
  { label: '合同审批', value: 'contract' },
  { label: '事前申请', value: 'pre_approval' },
  { label: '采购申请', value: 'purchase' }
]
