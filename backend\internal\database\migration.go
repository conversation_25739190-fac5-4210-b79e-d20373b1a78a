package database

import (
	"fmt"
	"log"

	"hospital-management/internal/models"

	"gorm.io/gorm"
)

// MigrationManager 数据库迁移管理器
type MigrationManager struct {
	db *gorm.DB
}

// NewMigrationManager 创建迁移管理器
func NewMigrationManager(db *gorm.DB) *MigrationManager {
	return &MigrationManager{db: db}
}

// RunMigrations 执行所有迁移
func (m *MigrationManager) RunMigrations() error {
	log.Println("开始执行数据库迁移...")

	// 1. 创建所有表结构
	if err := m.createTables(); err != nil {
		return fmt.Errorf("创建表结构失败: %w", err)
	}

	// 2. 创建索引
	if err := m.createIndexes(); err != nil {
		return fmt.Errorf("创建索引失败: %w", err)
	}

	// 3. 初始化基础数据
	if err := SeedData(m.db); err != nil {
		return fmt.Errorf("初始化基础数据失败: %w", err)
	}

	log.Println("数据库迁移完成!")
	return nil
}

// createTables 创建所有表结构
func (m *MigrationManager) createTables() error {
	log.Println("正在创建表结构...")

	// 根据流程功能点文档中的模型创建表
	return m.db.AutoMigrate(
		// 基础模型
		&models.Department{},
		&models.User{},

		// 预算管理模型
		&models.BudgetItem{},
		&models.BudgetFreeze{},

		// 费用类型模型
		&models.ExpenseType{},

		// 报销单模型
		&models.ExpenseClaim{},
		&models.ExpenseClaimDetail{},
		&models.ExpenseClaimAttachment{},

		// 事前申请和合同模型
		&models.PreApproval{},
		&models.Contract{},

		// 工作流模型
		&models.WorkflowDefinition{},
		&models.WorkflowNode{},
		&models.WorkflowInstance{},
		&models.WorkflowApprovalHistory{},
	)
}

// createIndexes 创建索引
func (m *MigrationManager) createIndexes() error {
	log.Println("正在创建索引...")

	indexes := []struct {
		table string
		sql   string
		desc  string
	}{
		// 用户表索引
		{"users", "CREATE INDEX IF NOT EXISTS idx_users_department_id ON users(department_id)", "用户部门索引"},

		// 预算表索引
		{"budget_items", "CREATE INDEX IF NOT EXISTS idx_budget_items_department_id ON budget_items(department_id)", "预算部门索引"},
		{"budget_freezes", "CREATE INDEX IF NOT EXISTS idx_budget_freezes_budget_item_id ON budget_freezes(budget_item_id)", "预算冻结索引"},

		// 报销单索引
		{"expense_claims", "CREATE INDEX IF NOT EXISTS idx_expense_claims_applicant_id ON expense_claims(applicant_id)", "报销申请人索引"},
		{"expense_claims", "CREATE INDEX IF NOT EXISTS idx_expense_claims_department_id ON expense_claims(department_id)", "报销部门索引"},
		{"expense_claim_details", "CREATE INDEX IF NOT EXISTS idx_expense_claim_details_claim_id ON expense_claim_details(claim_id)", "报销明细索引"},

		// 工作流索引
		{"workflow_instances", "CREATE INDEX IF NOT EXISTS idx_workflow_instances_business ON workflow_instances(business_id, business_type)", "工作流业务索引"},
		{"workflow_approval_history", "CREATE INDEX IF NOT EXISTS idx_workflow_approval_history_instance_id ON workflow_approval_history(instance_id)", "审批历史索引"},
	}

	for _, idx := range indexes {
		if err := m.db.Exec(idx.sql).Error; err != nil {
			log.Printf("⚠ 创建索引失败 (%s): %v", idx.desc, err)
		} else {
			log.Printf("✓ 已创建索引: %s", idx.desc)
		}
	}

	return nil
}
