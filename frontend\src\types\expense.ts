// 支出控制相关类型定义

export interface PayeeInfo {
  type: 'personal' | 'corporate'
  name: string
  bankName: string
  accountNo: string
  taxNo?: string
  address?: string
  phone?: string
}

export interface InvoiceInfo {
  code?: string
  number?: string
  date?: string
  amount?: number
  sellerName?: string
  sellerTaxNo?: string
  buyerName?: string
  buyerTaxNo?: string
  verified?: boolean
}

export interface ExpenseClaimDetail {
  id?: string
  expenseTypeId: string
  expenseTypeName?: string
  budgetItemId: string
  budgetItemName?: string
  amount: number
  description: string
  invoiceData?: InvoiceInfo
  availableBalance?: number
}

export interface ExpenseClaimAttachment {
  id?: string
  fileName: string
  originalName: string
  filePath: string
  fileSize?: number
  fileType?: string
  uploadedBy?: string
  createdAt?: string
}

export interface ExpenseClaim {
  id?: string
  title: string
  applicantId?: string
  applicantName?: string
  departmentId?: string
  departmentName?: string
  totalAmount: number
  status: 'draft' | 'pending' | 'approved' | 'rejected' | 'paid'
  payeeType: 'personal' | 'corporate'
  payeeInfo: PayeeInfo
  workflowInstanceId?: string
  relatedPreApprovalId?: string
  relatedContractId?: string
  submittedAt?: string
  approvedAt?: string
  paidAt?: string
  details: ExpenseClaimDetail[]
  attachments: ExpenseClaimAttachment[]
  createdAt?: string
  updatedAt?: string
}

export interface ExpenseType {
  id: string
  code: string
  name: string
  description: string
  isActive: boolean
}

export interface BudgetItem {
  id: string
  year: number
  subjectCode: string
  subjectName: string
  totalAmount: number
  usedAmount: number
  frozenAmount: number
  controlType: 'rigid' | 'flexible'
  expenseTypeId?: string
}

export interface BudgetBalance {
  budgetItemId: string
  budgetItemName: string
  totalAmount: number
  usedAmount: number
  frozenAmount: number
  availableBalance: number
  controlType: string
}

export interface PreApprovalSearchResult {
  id: string
  title: string
  amount: number
  status: string
}

export interface ContractSearchResult {
  id: string
  name: string
  contractNo: string
  amount: number
  status: string
}

// 表单相关类型
export interface ExpenseClaimFormData {
  title: string
  payeeType: 'personal' | 'corporate'
  payeeInfo: PayeeInfo
  relatedPreApprovalId?: string
  relatedContractId?: string
  details: ExpenseClaimDetail[]
  attachments: ExpenseClaimAttachment[]
}

// 查询参数类型
export interface ExpenseClaimQueryParams {
  status?: string
  keyword?: string
  startDate?: string
  endDate?: string
  page?: number
  pageSize?: number
}

// 统计数据类型
export interface ExpenseStatistics {
  totalAmount: number
  pendingCount: number
  approvedCount: number
  rejectedCount: number
  draftCount: number
  paidCount: number
  thisMonthAmount: number
  lastMonthAmount: number
}

// 状态选项
export const EXPENSE_CLAIM_STATUS_OPTIONS = [
  { label: '草稿', value: 'draft', color: 'default' },
  { label: '待审批', value: 'pending', color: 'processing' },
  { label: '已通过', value: 'approved', color: 'success' },
  { label: '已驳回', value: 'rejected', color: 'error' },
  { label: '已支付', value: 'paid', color: 'success' }
]

// 收款人类型选项
export const PAYEE_TYPE_OPTIONS = [
  { label: '对私', value: 'personal' },
  { label: '对公', value: 'corporate' }
]

// 预算控制类型选项
export const BUDGET_CONTROL_TYPE_OPTIONS = [
  { label: '刚性控制', value: 'rigid' },
  { label: '弹性控制', value: 'flexible' }
]
