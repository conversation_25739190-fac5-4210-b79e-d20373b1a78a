package dto

import (
	"time"

	"github.com/google/uuid"
)

// ApiResponse 标准API响应格式
type ApiResponse[T any] struct {
	Code    int    `json:"code"`    // 状态码，200表示成功，其他为标准HTTP状态码
	Message string `json:"message"` // 响应消息
	Data    T      `json:"data"`    // 响应数据
}

// NewSuccessResponse 创建成功响应
func NewSuccessResponse[T any](data T) ApiResponse[T] {
	return ApiResponse[T]{
		Code:    200,
		Message: "success",
		Data:    data,
	}
}

// NewErrorResponse 创建错误响应
func NewErrorResponse[T any](code int, message string) ApiResponse[T] {
	return ApiResponse[T]{
		Code:    code,
		Message: message,
	}
}

// PaginationRequest 分页请求
type PaginationRequest struct {
	Page     int `form:"page" validate:"omitempty,min=1"`
	PageSize int `form:"pageSize" validate:"omitempty,min=1,max=100"`
}

// PagedResponse 分页响应
type PagedResponse struct {
	Items    []interface{} `json:"items"`
	Total    int64         `json:"total"`
	Page     int           `json:"page"`
	PageSize int           `json:"pageSize"`
}

// BudgetStatisticsResponse 预算统计响应
type BudgetStatisticsResponse struct {
	TotalBudget     float64 `json:"totalBudget"`
	UsedBudget      float64 `json:"usedBudget"`
	FrozenBudget    float64 `json:"frozenBudget"`
	AvailableBudget float64 `json:"availableBudget"`
	UsageRate       float64 `json:"usageRate"`
}

// StartWorkflowRequest 启动工作流请求
type StartWorkflowRequest struct {
	DefinitionKey string                 `json:"definitionKey" binding:"required"`
	BusinessID    uuid.UUID              `json:"businessId" binding:"required"`
	BusinessType  string                 `json:"businessType" binding:"required"`
	StartUserID   uuid.UUID              `json:"startUserId" binding:"required"`
	Variables     map[string]interface{} `json:"variables"`
}

// StartWorkflowResponse 启动工作流响应
type StartWorkflowResponse struct {
	InstanceID    string `json:"instanceId"`
	CurrentNodeID string `json:"currentNodeId"`
	Status        string `json:"status"`
}

// ProcessApprovalRequest 处理审批请求
type ProcessApprovalRequest struct {
	InstanceID uuid.UUID `json:"instanceId" binding:"required"`
	Action     string    `json:"action" binding:"required" validate:"oneof=approve reject"`
	Comment    string    `json:"comment" validate:"max=500"`
}

// WorkflowInstanceResponse 工作流实例响应
type WorkflowInstanceResponse struct {
	ID            uuid.UUID              `json:"id"`
	DefinitionKey string                 `json:"definitionKey"`
	BusinessID    uuid.UUID              `json:"businessId"`
	BusinessType  string                 `json:"businessType"`
	Status        string                 `json:"status"`
	StartTime     time.Time              `json:"startTime"`
	EndTime       *time.Time             `json:"endTime"`
	Nodes         []WorkflowNodeResponse `json:"nodes"`
	Edges         []WorkflowEdgeResponse `json:"edges"`
}

// WorkflowNodeResponse 工作流节点响应
type WorkflowNodeResponse struct {
	ID       string `json:"id"`
	Label    string `json:"label"`
	Status   string `json:"status"`
	Assignee string `json:"assignee"`
}

// WorkflowEdgeResponse 工作流边响应
type WorkflowEdgeResponse struct {
	Source string `json:"source"`
	Target string `json:"target"`
}

// WorkflowPreviewResponse 工作流预览响应
type WorkflowPreviewResponse struct {
	Nodes []WorkflowNodeResponse `json:"nodes"`
	Edges []WorkflowEdgeResponse `json:"edges"`
}

// TerminateWorkflowRequest 终止工作流请求
type TerminateWorkflowRequest struct {
	Reason string `json:"reason" binding:"required" validate:"min=1,max=200"`
}
