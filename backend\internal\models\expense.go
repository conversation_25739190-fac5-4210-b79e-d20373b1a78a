package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// ExpenseClaim 报销单主表
type ExpenseClaim struct {
	BaseModel
	Title                string               `json:"title" gorm:"type:varchar(200);not null"`
	ApplicantID          uuid.UUID            `json:"applicant_id" gorm:"type:uuid;not null"`
	Applicant            *User                `json:"applicant,omitempty" gorm:"foreignKey:ApplicantID"`
	DepartmentID         uuid.UUID            `json:"department_id" gorm:"type:uuid;not null"`
	Department           *Department          `json:"department,omitempty" gorm:"foreignKey:DepartmentID"`
	TotalAmount          float64              `json:"total_amount" gorm:"type:decimal(18,2);not null"`
	Status               string               `json:"status" gorm:"type:varchar(20);not null;default:'draft'"` // draft, pending, approved, rejected, paid
	WorkflowInstanceID   *uuid.UUID           `json:"workflow_instance_id,omitempty" gorm:"type:uuid"`
	WorkflowInstance     *WorkflowInstance    `json:"workflow_instance,omitempty" gorm:"foreignKey:WorkflowInstanceID"`
	RelatedPreApprovalID *uuid.UUID           `json:"related_pre_approval_id,omitempty" gorm:"type:uuid"`
	RelatedContractID    *uuid.UUID           `json:"related_contract_id,omitempty" gorm:"type:uuid"`
	SubmittedAt          *time.Time           `json:"submitted_at,omitempty"`
	ApprovedAt           *time.Time           `json:"approved_at,omitempty"`
	PaidAt               *time.Time           `json:"paid_at,omitempty"`
	PayeeType            string               `json:"payee_type" gorm:"type:varchar(20);not null"` // personal, corporate
	PayeeInfo            json.RawMessage      `json:"payee_info" gorm:"type:jsonb"`
	Details              []ExpenseClaimDetail `json:"details,omitempty" gorm:"foreignKey:ClaimID"`
	Attachments          json.RawMessage      `json:"attachments,omitempty" gorm:"type:jsonb"`
}

// TableName 指定表名
func (ExpenseClaim) TableName() string {
	return "expense_claims"
}

// ExpenseClaimDetail 报销单明细表
type ExpenseClaimDetail struct {
	BaseModel
	ClaimID       uuid.UUID       `json:"claim_id" gorm:"type:uuid;not null"`
	Claim         *ExpenseClaim   `json:"claim,omitempty" gorm:"foreignKey:ClaimID"`
	ExpenseTypeID uuid.UUID       `json:"expense_type_id" gorm:"type:uuid;not null"`
	ExpenseType   *ExpenseType    `json:"expense_type,omitempty" gorm:"foreignKey:ExpenseTypeID"`
	BudgetItemID  uuid.UUID       `json:"budget_item_id" gorm:"type:uuid;not null"`
	BudgetItem    *BudgetItem     `json:"budget_item,omitempty" gorm:"foreignKey:BudgetItemID"`
	Amount        float64         `json:"amount" gorm:"type:decimal(18,2);not null"`
	Description   string          `json:"description" gorm:"type:text"`
	InvoiceData   json.RawMessage `json:"invoice_data,omitempty" gorm:"type:jsonb"`
}

// TableName 指定表名
func (ExpenseClaimDetail) TableName() string {
	return "expense_claim_details"
}

// ExpenseType 费用类型表
type ExpenseType struct {
	BaseModel
	Name        string `json:"name" gorm:"type:varchar(100);not null"`
	Code        string `json:"code" gorm:"type:varchar(50);uniqueIndex;not null"`
	Description string `json:"description" gorm:"type:text"`
	Status      string `json:"status" gorm:"type:varchar(20);default:'active'"`
}

// TableName 指定表名
func (ExpenseType) TableName() string {
	return "expense_types"
}

// ExpenseClaimAttachment 报销单附件表
type ExpenseClaimAttachment struct {
	BaseModel
	ClaimID      uuid.UUID `json:"claim_id" gorm:"type:uuid;not null;comment:报销单ID"`
	FileName     string    `json:"file_name" gorm:"not null;comment:文件名"`
	OriginalName string    `json:"original_name" gorm:"not null;comment:原始文件名"`
	FilePath     string    `json:"file_path" gorm:"not null;comment:文件路径"`
	FileSize     int64     `json:"file_size" gorm:"comment:文件大小"`
	FileType     string    `json:"file_type" gorm:"comment:文件类型"`
	UploadedBy   uuid.UUID `json:"uploaded_by" gorm:"type:uuid;comment:上传人ID"`
}

// TableName 指定表名
func (ExpenseClaimAttachment) TableName() string {
	return "expense_claim_attachments"
}

// PreApproval 事前申请表（简化版，用于关联）
type PreApproval struct {
	BaseModel
	Title       string    `json:"title" gorm:"not null;comment:申请事由"`
	Amount      float64   `json:"amount" gorm:"type:decimal(15,2);comment:申请金额"`
	ApplicantID uuid.UUID `json:"applicant_id" gorm:"type:uuid;not null;comment:申请人ID"`
	Status      string    `json:"status" gorm:"default:draft;comment:状态"`
}

// TableName 指定表名
func (PreApproval) TableName() string {
	return "pre_approvals"
}

// Contract 合同表（简化版，用于关联）
type Contract struct {
	BaseModel
	Name       string  `json:"name" gorm:"not null;comment:合同名称"`
	ContractNo string  `json:"contract_no" gorm:"not null;comment:合同编号"`
	Amount     float64 `json:"amount" gorm:"type:decimal(15,2);comment:合同金额"`
	Status     string  `json:"status" gorm:"default:draft;comment:状态"`
}

// TableName 指定表名
func (Contract) TableName() string {
	return "contracts"
}

// 状态常量
const (
	ExpenseClaimStatusDraft    = "draft"
	ExpenseClaimStatusPending  = "pending"
	ExpenseClaimStatusApproved = "approved"
	ExpenseClaimStatusRejected = "rejected"
	ExpenseClaimStatusPaid     = "paid"
)

// 收款人类型常量
const (
	PayeeTypePersonal  = "personal"
	PayeeTypeCorporate = "corporate"
)

// 业务方法
func (ec *ExpenseClaim) CanEdit() bool {
	return ec.Status == ExpenseClaimStatusDraft || ec.Status == ExpenseClaimStatusRejected
}

func (ec *ExpenseClaim) CanDelete() bool {
	return ec.Status == ExpenseClaimStatusDraft
}

func (ec *ExpenseClaim) CanWithdraw() bool {
	return ec.Status == ExpenseClaimStatusPending
}

func (ec *ExpenseClaim) CanApprove() bool {
	return ec.Status == ExpenseClaimStatusPending
}
