package controllers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"hospital-management/internal/dto"
	"hospital-management/internal/services"
	"hospital-management/pkg/logger"
	"hospital-management/pkg/response"
)

type WorkflowController struct {
	workflowService *services.WorkflowService
}

func NewWorkflowController(workflowService *services.WorkflowService) *WorkflowController {
	return &WorkflowController{
		workflowService: workflowService,
	}
}

// ProcessApproval 处理审批
// @Summary 处理审批
// @Description 审批人提交审批决定
// @Tags 工作流
// @Accept json
// @Produce json
// @Param request body dto.ProcessApprovalRequest true "审批数据"
// @Success 200 {object} response.Response "审批成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/approvals [post]
func (c *WorkflowController) ProcessApproval(ctx *gin.Context) {
	var req dto.ProcessApprovalRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("绑定JSON失败:", err)
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Error(ctx, http.StatusUnauthorized, "用户未登录")
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		response.Error(ctx, http.StatusUnauthorized, "用户ID格式错误")
		return
	}

	// 调用服务
	err := c.workflowService.ProcessApproval(&req, userUUID)
	if err != nil {
		logger.Error("处理审批失败:", err)
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, http.StatusOK, "审批成功", nil)
}

// GetWorkflowInstance 获取工作流实例详情
// @Summary 获取工作流实例详情
// @Description 获取流程实例的图形化展示所需数据
// @Tags 工作流
// @Accept json
// @Produce json
// @Param id path string true "工作流实例ID"
// @Success 200 {object} response.Response{data=dto.WorkflowInstanceResponse} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/workflow/instance/{id} [get]
func (c *WorkflowController) GetWorkflowInstance(ctx *gin.Context) {
	idStr := ctx.Param("id")
	instanceID, err := uuid.Parse(idStr)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的ID格式")
		return
	}

	result, err := c.workflowService.GetWorkflowInstance(instanceID)
	if err != nil {
		logger.Error("获取工作流实例失败:", err)
		if err.Error() == "工作流实例不存在" {
			response.Error(ctx, http.StatusNotFound, err.Error())
		} else {
			response.Error(ctx, http.StatusInternalServerError, err.Error())
		}
		return
	}

	response.Success(ctx, http.StatusOK, "获取成功", result)
}

// GetWorkflowPreview 获取审批流程预览
// @Summary 获取审批流程预览
// @Description 根据报销金额和类型，显示预计的审批流程节点
// @Tags 工作流
// @Accept json
// @Produce json
// @Param type query string true "业务类型"
// @Param amount query number false "金额"
// @Param departmentId query string false "部门ID"
// @Success 200 {object} response.Response{data=dto.WorkflowPreviewResponse} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/workflow/preview [get]
func (c *WorkflowController) GetWorkflowPreview(ctx *gin.Context) {
	businessType := ctx.Query("type")
	if businessType == "" {
		response.Error(ctx, http.StatusBadRequest, "业务类型不能为空")
		return
	}

	// 这里简化处理，返回固定的预览流程
	// 实际应用中应该根据业务类型、金额、部门等条件动态生成
	preview := dto.WorkflowPreviewResponse{
		Nodes: []dto.WorkflowNodeResponse{
			{
				ID:       "start",
				Label:    "发起申请",
				Status:   "completed",
				Assignee: "申请人",
			},
			{
				ID:       "dept_head",
				Label:    "部门负责人审批",
				Status:   "pending",
				Assignee: "部门负责人",
			},
			{
				ID:       "finance",
				Label:    "财务审核",
				Status:   "pending",
				Assignee: "财务科",
			},
			{
				ID:       "end",
				Label:    "审批完成",
				Status:   "pending",
				Assignee: "",
			},
		},
		Edges: []dto.WorkflowEdgeResponse{
			{Source: "start", Target: "dept_head"},
			{Source: "dept_head", Target: "finance"},
			{Source: "finance", Target: "end"},
		},
	}

	response.Success(ctx, http.StatusOK, "获取成功", preview)
}

// TerminateWorkflow 终止工作流实例
// @Summary 终止工作流实例
// @Description 终止指定的工作流实例
// @Tags 工作流
// @Accept json
// @Produce json
// @Param id path string true "工作流实例ID"
// @Param request body dto.TerminateWorkflowRequest true "终止原因"
// @Success 200 {object} response.Response "终止成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/workflow/instance/{id}/terminate [post]
func (c *WorkflowController) TerminateWorkflow(ctx *gin.Context) {
	instanceID := ctx.Param("id")
	if instanceID == "" {
		response.Error(ctx, http.StatusBadRequest, "工作流实例ID不能为空")
		return
	}

	var req dto.TerminateWorkflowRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("绑定JSON失败:", err)
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	err := c.workflowService.TerminateWorkflow(instanceID, req.Reason)
	if err != nil {
		logger.Error("终止工作流失败:", err)
		if err.Error() == "工作流实例不存在" {
			response.Error(ctx, http.StatusNotFound, err.Error())
		} else {
			response.Error(ctx, http.StatusInternalServerError, err.Error())
		}
		return
	}

	response.Success(ctx, http.StatusOK, "终止成功", nil)
}

// StartWorkflow 启动工作流
// @Summary 启动工作流
// @Description 启动一个新的工作流实例
// @Tags 工作流
// @Accept json
// @Produce json
// @Param request body dto.StartWorkflowRequest true "启动数据"
// @Success 201 {object} response.Response{data=dto.StartWorkflowResponse} "启动成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/workflow/start [post]
func (c *WorkflowController) StartWorkflow(ctx *gin.Context) {
	var req dto.StartWorkflowRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("绑定JSON失败:", err)
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	result, err := c.workflowService.StartWorkflow(&req)
	if err != nil {
		logger.Error("启动工作流失败:", err)
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, http.StatusCreated, "启动成功", result)
}

// GetPendingTasks 获取待办任务列表
// @Summary 获取待办任务列表
// @Description 获取当前用户的待办审批任务
// @Tags 工作流
// @Accept json
// @Produce json
// @Param businessType query string false "业务类型"
// @Param urgentLevel query string false "紧急程度"
// @Param keyword query string false "关键词"
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} response.Response{data=response.PageResponse{list=[]dto.ApprovalTaskResponse}} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/workflow/tasks/pending [get]
func (c *WorkflowController) GetPendingTasks(ctx *gin.Context) {
	var req dto.GetPendingTasksRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("绑定查询参数失败:", err)
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Error(ctx, http.StatusUnauthorized, "用户未登录")
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		response.Error(ctx, http.StatusUnauthorized, "用户ID格式错误")
		return
	}

	result, err := c.workflowService.GetPendingTasks(&req, userUUID)
	if err != nil {
		logger.Error("获取待办任务失败:", err)
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, http.StatusOK, "获取成功", result)
}

// GetApprovalHistory 获取审批历史
// @Summary 获取审批历史
// @Description 获取审批历史记录
// @Tags 工作流
// @Accept json
// @Produce json
// @Param businessType query string false "业务类型"
// @Param action query string false "审批动作"
// @Param approver query string false "审批人"
// @Param applicant query string false "申请人"
// @Param startDate query string false "开始日期"
// @Param endDate query string false "结束日期"
// @Param minAmount query number false "最小金额"
// @Param maxAmount query number false "最大金额"
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} response.Response{data=response.PageResponse{list=[]dto.ApprovalHistoryResponse}} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/workflow/approval-history [get]
func (c *WorkflowController) GetApprovalHistory(ctx *gin.Context) {
	var req dto.GetApprovalHistoryRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("绑定查询参数失败:", err)
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	result, err := c.workflowService.GetApprovalHistory(&req)
	if err != nil {
		logger.Error("获取审批历史失败:", err)
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, http.StatusOK, "获取成功", result)
}

// GetWorkflowDefinitions 获取工作流定义列表
// @Summary 获取工作流定义列表
// @Description 获取工作流定义列表
// @Tags 工作流
// @Accept json
// @Produce json
// @Param status query string false "状态"
// @Param keyword query string false "关键词"
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} response.Response{data=response.PageResponse{list=[]dto.WorkflowDefinitionResponse}} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/workflow/definitions [get]
func (c *WorkflowController) GetWorkflowDefinitions(ctx *gin.Context) {
	var req dto.GetWorkflowDefinitionsRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("绑定查询参数失败:", err)
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	result, err := c.workflowService.GetWorkflowDefinitions(&req)
	if err != nil {
		logger.Error("获取工作流定义失败:", err)
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, http.StatusOK, "获取成功", result)
}

// CreateWorkflowDefinition 创建工作流定义
// @Summary 创建工作流定义
// @Description 创建新的工作流定义
// @Tags 工作流
// @Accept json
// @Produce json
// @Param request body dto.WorkflowDefinitionRequest true "工作流定义数据"
// @Success 201 {object} response.Response{data=dto.WorkflowDefinitionResponse} "创建成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/workflow/definitions [post]
func (c *WorkflowController) CreateWorkflowDefinition(ctx *gin.Context) {
	var req dto.WorkflowDefinitionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("绑定JSON失败:", err)
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	result, err := c.workflowService.CreateWorkflowDefinition(&req)
	if err != nil {
		logger.Error("创建工作流定义失败:", err)
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, http.StatusCreated, "创建成功", result)
}
