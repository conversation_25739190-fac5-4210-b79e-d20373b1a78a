package main

import (
	"log"

	"golang.org/x/crypto/bcrypt"
	"github.com/google/uuid"

	"hospital-management/internal/config"
	"hospital-management/internal/database"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Setup("debug", "")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("加载配置失败:", err)
	}

	// 连接数据库
	db, err := database.Connect(cfg.Database)
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	log.Println("开始创建初始用户数据...")

	// 1. 创建部门
	departments := []models.Department{
		{
			BaseModel: models.BaseModel{ID: uuid.New()},
			Name:      "信息科",
			Code:      "IT",
			Level:     1,
			Sort:      1,
			Status:    "active",
		},
		{
			BaseModel: models.BaseModel{ID: uuid.New()},
			Name:      "财务科",
			Code:      "FINANCE",
			Level:     1,
			Sort:      2,
			Status:    "active",
		},
		{
			BaseModel: models.BaseModel{ID: uuid.New()},
			Name:      "医务科",
			Code:      "MEDICAL",
			Level:     1,
			Sort:      3,
			Status:    "active",
		},
		{
			BaseModel: models.BaseModel{ID: uuid.New()},
			Name:      "行政科",
			Code:      "ADMIN",
			Level:     1,
			Sort:      4,
			Status:    "active",
		},
	}

	for _, dept := range departments {
		var existing models.Department
		if err := db.Where("code = ?", dept.Code).First(&existing).Error; err != nil {
			if err.Error() == "record not found" {
				if err := db.Create(&dept).Error; err != nil {
					log.Printf("创建部门失败 %s: %v", dept.Name, err)
					continue
				}
				log.Printf("✓ 已创建部门: %s", dept.Name)
			} else {
				log.Printf("查询部门失败: %v", err)
				continue
			}
		} else {
			log.Printf("- 部门已存在: %s", dept.Name)
			dept.ID = existing.ID // 使用现有部门ID
		}
	}

	// 2. 创建用户
	// 默认密码：123456
	defaultPassword := "123456"
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(defaultPassword), bcrypt.DefaultCost)
	if err != nil {
		log.Fatal("生成密码哈希失败:", err)
	}

	users := []models.User{
		{
			BaseModel:    models.BaseModel{ID: uuid.New()},
			Username:     "admin",
			PasswordHash: string(hashedPassword),
			FullName:     "系统管理员",
			DepartmentID: departments[0].ID, // 信息科
			EmployeeID:   "ADMIN001",
			Title:        "系统管理员",
			Roles:        []string{"admin", "system"},
			Email:        "<EMAIL>",
			Phone:        "13800138000",
			Status:       "active",
		},
		{
			BaseModel:    models.BaseModel{ID: uuid.New()},
			Username:     "finance",
			PasswordHash: string(hashedPassword),
			FullName:     "财务主管",
			DepartmentID: departments[1].ID, // 财务科
			EmployeeID:   "FIN001",
			Title:        "财务主管",
			Roles:        []string{"finance", "approver"},
			Email:        "<EMAIL>",
			Phone:        "13800138001",
			Status:       "active",
		},
		{
			BaseModel:    models.BaseModel{ID: uuid.New()},
			Username:     "doctor",
			PasswordHash: string(hashedPassword),
			FullName:     "张医生",
			DepartmentID: departments[2].ID, // 医务科
			EmployeeID:   "DOC001",
			Title:        "主治医师",
			Roles:        []string{"doctor", "user"},
			Email:        "<EMAIL>",
			Phone:        "13800138002",
			Status:       "active",
		},
		{
			BaseModel:    models.BaseModel{ID: uuid.New()},
			Username:     "nurse",
			PasswordHash: string(hashedPassword),
			FullName:     "李护士",
			DepartmentID: departments[2].ID, // 医务科
			EmployeeID:   "NUR001",
			Title:        "护士长",
			Roles:        []string{"nurse", "user"},
			Email:        "<EMAIL>",
			Phone:        "13800138003",
			Status:       "active",
		},
		{
			BaseModel:    models.BaseModel{ID: uuid.New()},
			Username:     "manager",
			PasswordHash: string(hashedPassword),
			FullName:     "王经理",
			DepartmentID: departments[3].ID, // 行政科
			EmployeeID:   "MGR001",
			Title:        "行政经理",
			Roles:        []string{"manager", "approver"},
			Email:        "<EMAIL>",
			Phone:        "13800138004",
			Status:       "active",
		},
	}

	for _, user := range users {
		var existing models.User
		if err := db.Where("username = ?", user.Username).First(&existing).Error; err != nil {
			if err.Error() == "record not found" {
				if err := db.Create(&user).Error; err != nil {
					log.Printf("创建用户失败 %s: %v", user.Username, err)
					continue
				}
				log.Printf("✓ 已创建用户: %s (密码: %s)", user.Username, defaultPassword)
			} else {
				log.Printf("查询用户失败: %v", err)
				continue
			}
		} else {
			log.Printf("- 用户已存在: %s", user.Username)
		}
	}

	log.Println("\n=== 初始用户信息 ===")
	log.Println("默认密码：123456")
	log.Println("用户列表：")
	log.Println("1. admin - 系统管理员 (信息科)")
	log.Println("2. finance - 财务主管 (财务科)")
	log.Println("3. doctor - 张医生 (医务科)")
	log.Println("4. nurse - 李护士 (医务科)")
	log.Println("5. manager - 王经理 (行政科)")
	log.Println("\n初始用户数据创建完成！")
}
