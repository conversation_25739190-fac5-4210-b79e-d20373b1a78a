package services

import (
	"errors"
	"fmt"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
)

type BudgetService struct {
	db *gorm.DB
}

func NewBudgetService(db *gorm.DB) *BudgetService {
	return &BudgetService{
		db: db,
	}
}

// FreezeBudget 冻结预算
func (s *BudgetService) FreezeBudget(tx *gorm.DB, budgetItemID uuid.UUID, amount float64, businessID uuid.UUID, businessType string, userID uuid.UUID, description string) error {
	// 使用FOR UPDATE锁定预算项，防止并发问题
	var budgetItem models.BudgetItem
	if err := tx.Set("gorm:query_option", "FOR UPDATE").First(&budgetItem, "id = ?", budgetItemID).Error; err != nil {
		return fmt.Errorf("预算科目不存在: %v", err)
	}

	// 检查可用余额
	availableAmount := budgetItem.TotalAmount - budgetItem.UsedAmount - budgetItem.FrozenAmount
	if availableAmount < amount {
		return fmt.Errorf("预算余额不足，可用余额：%.2f，申请金额：%.2f", availableAmount, amount)
	}

	// 更新冻结金额
	if err := tx.Model(&budgetItem).Update("frozen_amount", gorm.Expr("frozen_amount + ?", amount)).Error; err != nil {
		return fmt.Errorf("更新冻结金额失败: %v", err)
	}

	// 创建冻结记录
	freeze := models.BudgetFreeze{
		BaseModel: models.BaseModel{
			ID: uuid.New(),
		},
		BudgetItemID: budgetItemID,
		Amount:       amount,
		BusinessID:   businessID,
		BusinessType: businessType,
		Status:       "active",
	}

	if err := tx.Create(&freeze).Error; err != nil {
		return fmt.Errorf("创建冻结记录失败: %v", err)
	}

	logger.Info(fmt.Sprintf("预算冻结成功 - 预算科目ID: %s, 金额: %.2f, 业务ID: %s", budgetItemID, amount, businessID))
	return nil
}

// ReleaseBudget 释放冻结的预算
func (s *BudgetService) ReleaseBudget(tx *gorm.DB, budgetItemID uuid.UUID, businessID uuid.UUID, businessType string) error {
	// 查找活跃的冻结记录
	var freeze models.BudgetFreeze
	if err := tx.First(&freeze, "budget_item_id = ? AND business_id = ? AND business_type = ? AND status = ?",
		budgetItemID, businessID, businessType, "active").Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Warn(fmt.Sprintf("未找到冻结记录 - 预算科目ID: %s, 业务ID: %s", budgetItemID, businessID))
			return nil // 没有冻结记录，直接返回成功
		}
		return fmt.Errorf("查找冻结记录失败: %v", err)
	}

	// 使用FOR UPDATE锁定预算项
	var budgetItem models.BudgetItem
	if err := tx.Set("gorm:query_option", "FOR UPDATE").First(&budgetItem, "id = ?", budgetItemID).Error; err != nil {
		return fmt.Errorf("预算科目不存在: %v", err)
	}

	// 更新预算项，减少冻结金额
	if err := tx.Model(&budgetItem).Update("frozen_amount", gorm.Expr("frozen_amount - ?", freeze.Amount)).Error; err != nil {
		return fmt.Errorf("更新冻结金额失败: %v", err)
	}

	// 更新冻结记录状态
	if err := tx.Model(&freeze).Update("status", "released").Error; err != nil {
		return fmt.Errorf("更新冻结记录状态失败: %v", err)
	}

	logger.Info(fmt.Sprintf("预算释放成功 - 预算科目ID: %s, 金额: %.2f, 业务ID: %s", budgetItemID, freeze.Amount, businessID))
	return nil
}

// UseBudget 使用预算（将冻结金额转为已使用金额）
func (s *BudgetService) UseBudget(tx *gorm.DB, budgetItemID uuid.UUID, amount float64, businessID uuid.UUID, businessType string) error {
	// 查找活跃的冻结记录
	var freeze models.BudgetFreeze
	if err := tx.First(&freeze, "budget_item_id = ? AND business_id = ? AND business_type = ? AND status = ?",
		budgetItemID, businessID, businessType, "active").Error; err != nil {
		return fmt.Errorf("未找到对应的冻结记录: %v", err)
	}

	// 检查金额是否匹配
	if freeze.Amount != amount {
		return fmt.Errorf("使用金额与冻结金额不匹配，冻结金额：%.2f，使用金额：%.2f", freeze.Amount, amount)
	}

	// 使用FOR UPDATE锁定预算项
	var budgetItem models.BudgetItem
	if err := tx.Set("gorm:query_option", "FOR UPDATE").First(&budgetItem, "id = ?", budgetItemID).Error; err != nil {
		return fmt.Errorf("预算科目不存在: %v", err)
	}

	// 更新预算项：减少冻结金额，增加已使用金额
	updates := map[string]interface{}{
		"frozen_amount": gorm.Expr("frozen_amount - ?", amount),
		"used_amount":   gorm.Expr("used_amount + ?", amount),
	}
	if err := tx.Model(&budgetItem).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新预算金额失败: %v", err)
	}

	// 更新冻结记录状态
	if err := tx.Model(&freeze).Update("status", "used").Error; err != nil {
		return fmt.Errorf("更新冻结记录状态失败: %v", err)
	}

	logger.Info(fmt.Sprintf("预算使用成功 - 预算科目ID: %s, 金额: %.2f, 业务ID: %s", budgetItemID, amount, businessID))
	return nil
}

// GetBudgetBalance 获取预算余额
func (s *BudgetService) GetBudgetBalance(budgetItemID uuid.UUID) (*dto.GetBudgetBalanceResponse, error) {
	var budgetItem models.BudgetItem
	if err := s.db.First(&budgetItem, "id = ?", budgetItemID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("预算科目不存在")
		}
		return nil, err
	}

	availableBalance := budgetItem.TotalAmount - budgetItem.UsedAmount - budgetItem.FrozenAmount

	return &dto.GetBudgetBalanceResponse{
		BudgetItemID:     budgetItem.ID,
		BudgetItemName:   budgetItem.SubjectName,
		TotalAmount:      budgetItem.TotalAmount,
		UsedAmount:       budgetItem.UsedAmount,
		FrozenAmount:     budgetItem.FrozenAmount,
		AvailableBalance: availableBalance,
		ControlType:      budgetItem.ControlType,
	}, nil
}

// GetBudgetItemsByUser 根据用户获取预算科目列表
func (s *BudgetService) GetBudgetItemsByUser(userID uuid.UUID, expenseTypeID *uuid.UUID) ([]dto.BudgetItemResponse, error) {
	// 获取用户信息
	var user models.User
	if err := s.db.First(&user, "id = ?", userID).Error; err != nil {
		return nil, fmt.Errorf("用户不存在: %v", err)
	}

	// 构建查询
	query := s.db.Model(&models.BudgetItem{}).
		Where("department_id = ? AND status = ?", user.DepartmentID, "active")

	// 如果指定了费用类型，可以在这里添加关联查询逻辑
	// 这里简化处理，实际应用中可能需要建立费用类型与预算科目的关联关系
	if expenseTypeID != nil {
		// TODO: 添加费用类型与预算科目的关联查询
	}

	var budgetItems []models.BudgetItem
	if err := query.Find(&budgetItems).Error; err != nil {
		return nil, err
	}

	// 构建响应
	var result []dto.BudgetItemResponse
	for _, item := range budgetItems {
		result = append(result, dto.BudgetItemResponse{
			ID:           item.ID,
			Year:         item.Year,
			SubjectCode:  item.SubjectCode,
			SubjectName:  item.SubjectName,
			TotalAmount:  item.TotalAmount,
			UsedAmount:   item.UsedAmount,
			FrozenAmount: item.FrozenAmount,
			ControlType:  item.ControlType,
		})
	}

	return result, nil
}

// GetBudgetStatistics 获取预算统计信息
func (s *BudgetService) GetBudgetStatistics(departmentID uuid.UUID, year int) (*dto.BudgetStatisticsResponse, error) {
	var stats dto.BudgetStatisticsResponse

	// 查询部门年度预算总额
	err := s.db.Model(&models.BudgetItem{}).
		Where("department_id = ? AND year = ? AND status = ?", departmentID, year, "active").
		Select("COALESCE(SUM(total_amount), 0)").
		Scan(&stats.TotalBudget).Error
	if err != nil {
		return nil, err
	}

	// 查询已使用预算
	err = s.db.Model(&models.BudgetItem{}).
		Where("department_id = ? AND year = ? AND status = ?", departmentID, year, "active").
		Select("COALESCE(SUM(used_amount), 0)").
		Scan(&stats.UsedBudget).Error
	if err != nil {
		return nil, err
	}

	// 查询冻结预算
	err = s.db.Model(&models.BudgetItem{}).
		Where("department_id = ? AND year = ? AND status = ?", departmentID, year, "active").
		Select("COALESCE(SUM(frozen_amount), 0)").
		Scan(&stats.FrozenBudget).Error
	if err != nil {
		return nil, err
	}

	// 计算可用预算
	stats.AvailableBudget = stats.TotalBudget - stats.UsedBudget - stats.FrozenBudget

	// 计算使用率
	if stats.TotalBudget > 0 {
		stats.UsageRate = (stats.UsedBudget / stats.TotalBudget) * 100
	}

	return &stats, nil
}
