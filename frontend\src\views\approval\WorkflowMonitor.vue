<template>
  <div class="workflow-monitor">
    <div class="page-header">
      <h2>工作流监控</h2>
      <a-space>
        <a-button @click="refreshData">
          <ReloadOutlined />
          刷新
        </a-button>
        <a-button @click="exportReport">
          <ExportOutlined />
          导出报告
        </a-button>
      </a-space>
    </div>

    <!-- 实时统计 -->
    <div class="real-time-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="运行中实例"
              :value="realTimeStats.runningInstances"
              :value-style="{ color: '#1890ff' }"
            >
              <template #suffix>
                <span class="trend-up">↑ 12%</span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="待处理任务"
              :value="realTimeStats.pendingTasks"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #suffix>
                <span class="trend-down">↓ 5%</span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="今日完成"
              :value="realTimeStats.todayCompleted"
              :value-style="{ color: '#52c41a' }"
            >
              <template #suffix>
                <span class="trend-up">↑ 8%</span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="超时实例"
              :value="realTimeStats.overtimeInstances"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #suffix>
                <span class="trend-down">↓ 15%</span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <a-row :gutter="16">
        <!-- 流程处理趋势 -->
        <a-col :span="12">
          <a-card title="流程处理趋势" class="chart-card">
            <div ref="trendChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
        
        <!-- 业务类型分布 -->
        <a-col :span="12">
          <a-card title="业务类型分布" class="chart-card">
            <div ref="pieChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>
      
      <a-row :gutter="16" style="margin-top: 16px;">
        <!-- 审批效率分析 -->
        <a-col :span="12">
          <a-card title="审批效率分析" class="chart-card">
            <div ref="efficiencyChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
        
        <!-- 部门处理量对比 -->
        <a-col :span="12">
          <a-card title="部门处理量对比" class="chart-card">
            <div ref="departmentChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 异常监控 -->
    <a-card title="异常监控" class="exception-monitor">
      <a-tabs>
        <a-tab-pane key="overtime" tab="超时实例">
          <a-table
            :columns="overtimeColumns"
            :data-source="overtimeInstances"
            :loading="overtimeLoading"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'businessType'">
                <a-tag>{{ getBusinessTypeText(record.businessType) }}</a-tag>
              </template>
              <template v-if="column.key === 'overtimeHours'">
                <span style="color: #ff4d4f;">{{ record.overtimeHours }}小时</span>
              </template>
              <template v-if="column.key === 'actions'">
                <a-space>
                  <a-button size="small" @click="viewInstance(record)">查看</a-button>
                  <a-button size="small" type="primary" @click="urgeProcess(record)">催办</a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
        
        <a-tab-pane key="stuck" tab="卡住实例">
          <a-table
            :columns="stuckColumns"
            :data-source="stuckInstances"
            :loading="stuckLoading"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'businessType'">
                <a-tag>{{ getBusinessTypeText(record.businessType) }}</a-tag>
              </template>
              <template v-if="column.key === 'stuckDays'">
                <span style="color: #ff4d4f;">{{ record.stuckDays }}天</span>
              </template>
              <template v-if="column.key === 'actions'">
                <a-space>
                  <a-button size="small" @click="viewInstance(record)">查看</a-button>
                  <a-button size="small" danger @click="terminateInstance(record)">终止</a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
        
        <a-tab-pane key="errors" tab="错误实例">
          <a-table
            :columns="errorColumns"
            :data-source="errorInstances"
            :loading="errorLoading"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'businessType'">
                <a-tag>{{ getBusinessTypeText(record.businessType) }}</a-tag>
              </template>
              <template v-if="column.key === 'errorType'">
                <a-tag color="red">{{ record.errorType }}</a-tag>
              </template>
              <template v-if="column.key === 'actions'">
                <a-space>
                  <a-button size="small" @click="viewInstance(record)">查看</a-button>
                  <a-button size="small" type="primary" @click="retryInstance(record)">重试</a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 性能指标 -->
    <a-card title="性能指标" class="performance-metrics">
      <a-row :gutter="16">
        <a-col :span="8">
          <div class="metric-item">
            <div class="metric-title">平均处理时长</div>
            <div class="metric-value">{{ performanceMetrics.avgProcessTime }}小时</div>
            <div class="metric-trend trend-up">较上周 ↑ 0.5小时</div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="metric-item">
            <div class="metric-title">审批通过率</div>
            <div class="metric-value">{{ performanceMetrics.approvalRate }}%</div>
            <div class="metric-trend trend-up">较上周 ↑ 2.3%</div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="metric-item">
            <div class="metric-title">系统响应时间</div>
            <div class="metric-value">{{ performanceMetrics.responseTime }}ms</div>
            <div class="metric-trend trend-down">较上周 ↓ 50ms</div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 催办弹窗 -->
    <a-modal
      v-model:visible="urgeModalVisible"
      title="催办处理"
      :confirm-loading="urgeLoading"
      @ok="confirmUrge"
      @cancel="cancelUrge"
    >
      <div v-if="currentInstance">
        <p><strong>实例ID:</strong> {{ currentInstance.id }}</p>
        <p><strong>业务类型:</strong> {{ getBusinessTypeText(currentInstance.businessType) }}</p>
        <p><strong>当前节点:</strong> {{ currentInstance.currentNodeName }}</p>
        <p><strong>处理人:</strong> {{ currentInstance.currentAssignee }}</p>
      </div>
      
      <a-form-item label="催办消息">
        <a-textarea v-model:value="urgeMessage" placeholder="请输入催办消息" :rows="3" />
      </a-form-item>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined, ExportOutlined } from '@ant-design/icons-vue'

// 响应式数据
const trendChartRef = ref<HTMLElement>()
const pieChartRef = ref<HTMLElement>()
const efficiencyChartRef = ref<HTMLElement>()
const departmentChartRef = ref<HTMLElement>()

const overtimeLoading = ref(false)
const stuckLoading = ref(false)
const errorLoading = ref(false)
const urgeModalVisible = ref(false)
const urgeLoading = ref(false)

const overtimeInstances = ref([])
const stuckInstances = ref([])
const errorInstances = ref([])
const currentInstance = ref(null)
const urgeMessage = ref('')

// 实时统计数据
const realTimeStats = reactive({
  runningInstances: 45,
  pendingTasks: 23,
  todayCompleted: 67,
  overtimeInstances: 8
})

// 性能指标
const performanceMetrics = reactive({
  avgProcessTime: 3.2,
  approvalRate: 87.5,
  responseTime: 245
})

// 表格列定义
const overtimeColumns = [
  { title: '实例ID', dataIndex: 'id', key: 'id', width: 120 },
  { title: '业务类型', key: 'businessType', width: 100 },
  { title: '申请标题', dataIndex: 'title', key: 'title', ellipsis: true },
  { title: '当前节点', dataIndex: 'currentNodeName', key: 'currentNodeName', width: 120 },
  { title: '处理人', dataIndex: 'currentAssignee', key: 'currentAssignee', width: 100 },
  { title: '超时时长', key: 'overtimeHours', width: 100 },
  { title: '操作', key: 'actions', width: 150 }
]

const stuckColumns = [
  { title: '实例ID', dataIndex: 'id', key: 'id', width: 120 },
  { title: '业务类型', key: 'businessType', width: 100 },
  { title: '申请标题', dataIndex: 'title', key: 'title', ellipsis: true },
  { title: '当前节点', dataIndex: 'currentNodeName', key: 'currentNodeName', width: 120 },
  { title: '卡住时长', key: 'stuckDays', width: 100 },
  { title: '操作', key: 'actions', width: 150 }
]

const errorColumns = [
  { title: '实例ID', dataIndex: 'id', key: 'id', width: 120 },
  { title: '业务类型', key: 'businessType', width: 100 },
  { title: '申请标题', dataIndex: 'title', key: 'title', ellipsis: true },
  { title: '错误类型', key: 'errorType', width: 120 },
  { title: '错误信息', dataIndex: 'errorMessage', key: 'errorMessage', ellipsis: true },
  { title: '操作', key: 'actions', width: 150 }
]

// 获取业务类型文本
const getBusinessTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    expense_claim: '报销申请',
    contract: '合同审批',
    pre_approval: '事前申请',
    purchase: '采购申请'
  }
  return textMap[type] || type
}

// 初始化图表
const initCharts = async () => {
  await nextTick()

  // 这里应该使用实际的图表库如 ECharts 或 AntV/G2
  // 由于没有安装图表库，这里只是占位
  console.log('初始化图表...')

  // 模拟图表初始化
  if (trendChartRef.value) {
    trendChartRef.value.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">流程处理趋势图表</div>'
  }

  if (pieChartRef.value) {
    pieChartRef.value.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">业务类型分布图表</div>'
  }

  if (efficiencyChartRef.value) {
    efficiencyChartRef.value.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">审批效率分析图表</div>'
  }

  if (departmentChartRef.value) {
    departmentChartRef.value.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">部门处理量对比图表</div>'
  }
}

// 加载异常数据
const loadExceptionData = async () => {
  overtimeLoading.value = true
  stuckLoading.value = true
  errorLoading.value = true

  try {
    // 模拟数据
    overtimeInstances.value = [
      {
        id: 'instance1',
        businessType: 'expense_claim',
        title: '办公用品采购报销',
        currentNodeName: '部门负责人审批',
        currentAssignee: '张三',
        overtimeHours: 12
      }
    ]

    stuckInstances.value = [
      {
        id: 'instance2',
        businessType: 'contract',
        title: '设备采购合同',
        currentNodeName: '法务审核',
        stuckDays: 5
      }
    ]

    errorInstances.value = [
      {
        id: 'instance3',
        businessType: 'pre_approval',
        title: '差旅申请',
        errorType: '系统错误',
        errorMessage: '预算服务连接超时'
      }
    ]
  } catch (error) {
    console.error('加载异常数据失败:', error)
  } finally {
    overtimeLoading.value = false
    stuckLoading.value = false
    errorLoading.value = false
  }
}

// 查看实例
const viewInstance = (record: any) => {
  console.log('查看实例:', record)
  // 实现查看逻辑
}

// 催办处理
const urgeProcess = (record: any) => {
  currentInstance.value = record
  urgeMessage.value = ''
  urgeModalVisible.value = true
}

// 确认催办
const confirmUrge = async () => {
  if (!urgeMessage.value.trim()) {
    message.warning('请输入催办消息')
    return
  }

  urgeLoading.value = true
  try {
    // 调用催办API
    message.success('催办消息已发送')
    urgeModalVisible.value = false
  } catch (error) {
    console.error('催办失败:', error)
    message.error('催办失败')
  } finally {
    urgeLoading.value = false
  }
}

// 取消催办
const cancelUrge = () => {
  urgeModalVisible.value = false
  currentInstance.value = null
  urgeMessage.value = ''
}

// 终止实例
const terminateInstance = (record: any) => {
  console.log('终止实例:', record)
  // 实现终止逻辑
}

// 重试实例
const retryInstance = (record: any) => {
  console.log('重试实例:', record)
  // 实现重试逻辑
}

// 导出报告
const exportReport = () => {
  message.info('导出报告功能开发中...')
}

// 刷新数据
const refreshData = async () => {
  await Promise.all([
    loadExceptionData(),
    initCharts()
  ])
}

// 初始化
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.workflow-monitor {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.real-time-stats {
  margin-bottom: 24px;
}

.trend-up {
  color: #52c41a;
  font-size: 12px;
}

.trend-down {
  color: #ff4d4f;
  font-size: 12px;
}

.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  height: 350px;
}

.exception-monitor {
  margin-bottom: 24px;
}

.performance-metrics {
  margin-bottom: 24px;
}

.metric-item {
  text-align: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.metric-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: 500;
  color: #1890ff;
  margin-bottom: 4px;
}

.metric-trend {
  font-size: 12px;
}
</style>
</script>
