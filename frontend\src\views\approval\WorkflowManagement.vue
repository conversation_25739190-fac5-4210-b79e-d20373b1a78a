<template>
  <div class="workflow-management">
    <div class="page-header">
      <h2>工作流管理</h2>
      <a-space>
        <a-button type="primary" @click="showCreateModal">
          <PlusOutlined />
          新建流程定义
        </a-button>
        <a-button @click="refreshData">
          <ReloadOutlined />
          刷新
        </a-button>
      </a-space>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="活跃流程定义"
              :value="statistics.activeDefinitions"
              :value-style="{ color: '#3f8600' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="运行中实例"
              :value="statistics.runningInstances"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="今日完成"
              :value="statistics.todayCompleted"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="平均处理时长"
              :value="statistics.avgProcessTime"
              suffix="小时"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选器 -->
    <div class="filter-bar">
      <a-space>
        <a-select v-model:value="filterStatus" placeholder="状态" style="width: 120px" allowClear>
          <a-select-option value="active">活跃</a-select-option>
          <a-select-option value="inactive">停用</a-select-option>
        </a-select>
        <a-input v-model:value="filterKeyword" placeholder="搜索流程名称" style="width: 200px" />
        <a-button type="primary" @click="loadWorkflowDefinitions">搜索</a-button>
      </a-space>
    </div>

    <!-- 流程定义列表 -->
    <a-card title="流程定义" class="definitions-card">
      <a-table
        :columns="definitionColumns"
        :data-source="workflowDefinitions"
        :loading="definitionsLoading"
        :pagination="definitionsPagination"
        @change="handleDefinitionsTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 'active' ? 'green' : 'red'">
              {{ record.status === 'active' ? '活跃' : '停用' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button size="small" @click="viewDefinition(record)">查看</a-button>
              <a-button size="small" @click="editDefinition(record)">编辑</a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="toggleDefinitionStatus(record)">
                      {{ record.status === 'active' ? '停用' : '启用' }}
                    </a-menu-item>
                    <a-menu-item @click="viewInstances(record)">查看实例</a-menu-item>
                    <a-menu-divider />
                    <a-menu-item danger @click="deleteDefinition(record)">删除</a-menu-item>
                  </a-menu>
                </template>
                <a-button size="small">
                  更多 <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 流程实例列表 -->
    <a-card title="流程实例" class="instances-card">
      <template #extra>
        <a-space>
          <a-select v-model:value="instanceFilterStatus" placeholder="实例状态" style="width: 120px" allowClear>
            <a-select-option value="active">运行中</a-select-option>
            <a-select-option value="completed">已完成</a-select-option>
            <a-select-option value="terminated">已终止</a-select-option>
            <a-select-option value="rejected">已驳回</a-select-option>
          </a-select>
          <a-button @click="loadWorkflowInstances">刷新</a-button>
        </a-space>
      </template>
      
      <a-table
        :columns="instanceColumns"
        :data-source="workflowInstances"
        :loading="instancesLoading"
        :pagination="instancesPagination"
        @change="handleInstancesTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getInstanceStatusColor(record.status)">
              {{ getInstanceStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'duration'">
            {{ calculateDuration(record.startTime, record.endTime) }}
          </template>
          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button size="small" @click="viewInstance(record)">查看</a-button>
              <a-button 
                v-if="record.status === 'active'" 
                size="small" 
                danger 
                @click="terminateInstance(record)"
              >
                终止
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑流程定义弹窗 -->
    <a-modal
      v-model:visible="definitionModalVisible"
      :title="definitionModalTitle"
      width="800px"
      :confirm-loading="definitionModalLoading"
      @ok="saveDefinition"
      @cancel="cancelDefinitionModal"
    >
      <a-form :model="definitionForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="流程标识" required>
              <a-input v-model:value="definitionForm.key" placeholder="请输入流程标识" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="流程名称" required>
              <a-input v-model:value="definitionForm.name" placeholder="请输入流程名称" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="流程描述">
          <a-textarea v-model:value="definitionForm.description" :rows="3" />
        </a-form-item>
        
        <a-form-item label="状态">
          <a-radio-group v-model:value="definitionForm.status">
            <a-radio value="active">活跃</a-radio>
            <a-radio value="inactive">停用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 终止实例弹窗 -->
    <a-modal
      v-model:visible="terminateModalVisible"
      title="终止流程实例"
      :confirm-loading="terminateModalLoading"
      @ok="confirmTerminate"
      @cancel="cancelTerminate"
    >
      <p>确定要终止此流程实例吗？此操作不可撤销。</p>
      <a-form-item label="终止原因">
        <a-textarea v-model:value="terminateReason" placeholder="请输入终止原因" :rows="3" />
      </a-form-item>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import { workflowApi } from '@/api/workflow'
import type { WorkflowDefinition, WorkflowInstance } from '@/types/workflow'

// 响应式数据
const definitionsLoading = ref(false)
const instancesLoading = ref(false)
const definitionModalVisible = ref(false)
const definitionModalLoading = ref(false)
const terminateModalVisible = ref(false)
const terminateModalLoading = ref(false)

const workflowDefinitions = ref<WorkflowDefinition[]>([])
const workflowInstances = ref<WorkflowInstance[]>([])
const currentInstance = ref<WorkflowInstance | null>(null)
const terminateReason = ref('')

// 筛选条件
const filterStatus = ref<string>()
const filterKeyword = ref('')
const instanceFilterStatus = ref<string>()

// 统计数据
const statistics = reactive({
  activeDefinitions: 5,
  runningInstances: 23,
  todayCompleted: 15,
  avgProcessTime: 2.5
})

// 表单数据
const definitionForm = reactive({
  id: '',
  key: '',
  name: '',
  description: '',
  status: 'active' as 'active' | 'inactive'
})

// 分页配置
const definitionsPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true
})

const instancesPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true
})

// 计算属性
const definitionModalTitle = computed(() => {
  return definitionForm.id ? '编辑流程定义' : '新建流程定义'
})

// 表格列定义
const definitionColumns = [
  { title: '流程标识', dataIndex: 'key', key: 'key' },
  { title: '流程名称', dataIndex: 'name', key: 'name' },
  { title: '版本', dataIndex: 'version', key: 'version' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '描述', dataIndex: 'description', key: 'description', ellipsis: true },
  { title: '操作', key: 'actions', width: 200 }
]

const instanceColumns = [
  { title: '实例ID', dataIndex: 'id', key: 'id' },
  { title: '流程定义', dataIndex: 'definitionKey', key: 'definitionKey' },
  { title: '业务ID', dataIndex: 'businessId', key: 'businessId' },
  { title: '业务类型', dataIndex: 'businessType', key: 'businessType' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '开始时间', dataIndex: 'startTime', key: 'startTime' },
  { title: '持续时间', key: 'duration' },
  { title: '操作', key: 'actions', width: 150 }
]

// 获取实例状态颜色
const getInstanceStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    active: 'processing',
    completed: 'success',
    terminated: 'default',
    rejected: 'error'
  }
  return colorMap[status] || 'default'
}

// 获取实例状态文本
const getInstanceStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    active: '运行中',
    completed: '已完成',
    terminated: '已终止',
    rejected: '已驳回'
  }
  return textMap[status] || status
}

// 计算持续时间
const calculateDuration = (startTime: string, endTime?: string) => {
  const start = new Date(startTime)
  const end = endTime ? new Date(endTime) : new Date()
  const duration = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60))
  return `${duration}小时`
}

// 加载流程定义
const loadWorkflowDefinitions = async () => {
  definitionsLoading.value = true
  try {
    // 模拟数据，实际应该调用API
    workflowDefinitions.value = [
      {
        id: '1',
        key: 'expense_claim_process',
        name: '报销申请流程',
        description: '员工报销申请审批流程',
        version: 1,
        status: 'active',
        nodes: []
      },
      {
        id: '2',
        key: 'contract_approval_process',
        name: '合同审批流程',
        description: '合同签署审批流程',
        version: 2,
        status: 'active',
        nodes: []
      }
    ]
    definitionsPagination.total = workflowDefinitions.value.length
  } catch (error) {
    console.error('加载流程定义失败:', error)
    message.error('加载流程定义失败')
  } finally {
    definitionsLoading.value = false
  }
}

// 加载流程实例
const loadWorkflowInstances = async () => {
  instancesLoading.value = true
  try {
    // 模拟数据，实际应该调用API
    workflowInstances.value = [
      {
        id: 'instance1',
        definitionKey: 'expense_claim_process',
        businessId: 'claim1',
        businessType: 'expense_claim',
        status: 'active',
        startTime: new Date(Date.now() - 3600000).toISOString(),
        nodes: [],
        edges: []
      },
      {
        id: 'instance2',
        definitionKey: 'contract_approval_process',
        businessId: 'contract1',
        businessType: 'contract',
        status: 'completed',
        startTime: new Date(Date.now() - 86400000).toISOString(),
        endTime: new Date(Date.now() - 3600000).toISOString(),
        nodes: [],
        edges: []
      }
    ]
    instancesPagination.total = workflowInstances.value.length
  } catch (error) {
    console.error('加载流程实例失败:', error)
    message.error('加载流程实例失败')
  } finally {
    instancesLoading.value = false
  }
}

// 表格变化处理
const handleDefinitionsTableChange = (pagination: any) => {
  definitionsPagination.current = pagination.current
  definitionsPagination.pageSize = pagination.pageSize
  loadWorkflowDefinitions()
}

const handleInstancesTableChange = (pagination: any) => {
  instancesPagination.current = pagination.current
  instancesPagination.pageSize = pagination.pageSize
  loadWorkflowInstances()
}

// 显示创建弹窗
const showCreateModal = () => {
  resetDefinitionForm()
  definitionModalVisible.value = true
}

// 重置表单
const resetDefinitionForm = () => {
  Object.assign(definitionForm, {
    id: '',
    key: '',
    name: '',
    description: '',
    status: 'active'
  })
}

// 查看流程定义
const viewDefinition = (record: WorkflowDefinition) => {
  console.log('查看流程定义:', record)
  // 实现查看逻辑
}

// 编辑流程定义
const editDefinition = (record: WorkflowDefinition) => {
  Object.assign(definitionForm, record)
  definitionModalVisible.value = true
}

// 切换流程定义状态
const toggleDefinitionStatus = async (record: WorkflowDefinition) => {
  try {
    const newStatus = record.status === 'active' ? 'inactive' : 'active'
    // 调用API更新状态
    record.status = newStatus
    message.success(`流程定义已${newStatus === 'active' ? '启用' : '停用'}`)
  } catch (error) {
    console.error('切换状态失败:', error)
    message.error('操作失败')
  }
}

// 查看流程实例
const viewInstances = (record: WorkflowDefinition) => {
  // 筛选该定义的实例
  instanceFilterStatus.value = undefined
  loadWorkflowInstances()
}

// 删除流程定义
const deleteDefinition = (record: WorkflowDefinition) => {
  // 实现删除逻辑
  console.log('删除流程定义:', record)
}

// 保存流程定义
const saveDefinition = async () => {
  definitionModalLoading.value = true
  try {
    // 调用API保存
    message.success('保存成功')
    definitionModalVisible.value = false
    loadWorkflowDefinitions()
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败')
  } finally {
    definitionModalLoading.value = false
  }
}

// 取消编辑
const cancelDefinitionModal = () => {
  definitionModalVisible.value = false
  resetDefinitionForm()
}

// 查看流程实例
const viewInstance = (record: WorkflowInstance) => {
  console.log('查看流程实例:', record)
  // 实现查看逻辑
}

// 终止流程实例
const terminateInstance = (record: WorkflowInstance) => {
  currentInstance.value = record
  terminateReason.value = ''
  terminateModalVisible.value = true
}

// 确认终止
const confirmTerminate = async () => {
  if (!currentInstance.value || !terminateReason.value.trim()) {
    message.warning('请输入终止原因')
    return
  }

  terminateModalLoading.value = true
  try {
    await workflowApi.terminateWorkflow(currentInstance.value.id, {
      reason: terminateReason.value
    })
    message.success('流程实例已终止')
    terminateModalVisible.value = false
    loadWorkflowInstances()
  } catch (error) {
    console.error('终止失败:', error)
    message.error('终止失败')
  } finally {
    terminateModalLoading.value = false
  }
}

// 取消终止
const cancelTerminate = () => {
  terminateModalVisible.value = false
  currentInstance.value = null
  terminateReason.value = ''
}

// 刷新数据
const refreshData = async () => {
  await Promise.all([
    loadWorkflowDefinitions(),
    loadWorkflowInstances()
  ])
}

// 初始化
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.workflow-management {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.statistics-cards {
  margin-bottom: 24px;
}

.filter-bar {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.definitions-card {
  margin-bottom: 24px;
}

.instances-card {
  margin-bottom: 24px;
}
</style>
</script>
