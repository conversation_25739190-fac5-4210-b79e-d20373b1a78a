package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
)

type ExpenseService struct {
	db              *gorm.DB
	budgetService   *BudgetService
	workflowService *WorkflowService
}

func NewExpenseService(db *gorm.DB, budgetService *BudgetService, workflowService *WorkflowService) *ExpenseService {
	return &ExpenseService{
		db:              db,
		budgetService:   budgetService,
		workflowService: workflowService,
	}
}

// CreateClaim 创建报销申请
func (s *ExpenseService) CreateClaim(req *dto.CreateExpenseClaimRequest, userID uuid.UUID) (*dto.ExpenseClaimResponse, error) {
	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 验证用户信息
	var user models.User
	if err := tx.Preload("Department").First(&user, "id = ?", userID).Error; err != nil {
		tx.Rollback()
		return nil, errors.New("用户不存在")
	}

	// 转换PayeeInfo
	payeeInfoBytes, err := json.Marshal(req.PayeeInfo)
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("序列化收款人信息失败: %v", err)
	}

	// 创建报销单主记录
	claim := &models.ExpenseClaim{
		BaseModel: models.BaseModel{
			ID: uuid.New(),
		},
		Title:                req.Title,
		ApplicantID:          userID,
		DepartmentID:         user.DepartmentID,
		TotalAmount:          0, // 将通过明细计算
		Status:               "draft",
		PayeeType:            req.PayeeType,
		PayeeInfo:            payeeInfoBytes,
		RelatedPreApprovalID: req.RelatedPreApprovalID,
		RelatedContractID:    req.RelatedContractID,
	}

	// 处理费用明细
	var totalAmount float64
	for _, detailReq := range req.Details {
		// 验证预算科目
		var budgetItem models.BudgetItem
		if err := tx.First(&budgetItem, "id = ? AND department_id = ?", detailReq.BudgetItemID, user.DepartmentID).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("预算科目不存在或不属于当前部门")
		}

		// 检查预算余额
		availableAmount := budgetItem.TotalAmount - budgetItem.UsedAmount - budgetItem.FrozenAmount
		if availableAmount < detailReq.Amount {
			tx.Rollback()
			return nil, fmt.Errorf("预算科目 %s 余额不足，可用余额：%.2f，申请金额：%.2f",
				budgetItem.SubjectName, availableAmount, detailReq.Amount)
		}

		// 转换InvoiceData
		invoiceDataBytes, err := json.Marshal(detailReq.InvoiceData)
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("序列化发票信息失败: %v", err)
		}

		// 创建明细记录
		detail := models.ExpenseClaimDetail{
			BaseModel: models.BaseModel{
				ID: uuid.New(),
			},
			ClaimID:       claim.ID,
			ExpenseTypeID: detailReq.ExpenseTypeID,
			BudgetItemID:  detailReq.BudgetItemID,
			Amount:        detailReq.Amount,
			Description:   detailReq.Description,
			InvoiceData:   invoiceDataBytes,
		}

		claim.Details = append(claim.Details, detail)
		totalAmount += detailReq.Amount
	}

	claim.TotalAmount = totalAmount

	// 保存报销单
	if err := tx.Create(claim).Error; err != nil {
		tx.Rollback()
		logger.Error("创建报销单失败:", err)
		return nil, errors.New("创建报销单失败")
	}

	// 冻结预算
	for _, detail := range claim.Details {
		if err := s.budgetService.FreezeBudget(tx, detail.BudgetItemID, detail.Amount,
			claim.ID, "expense_claim", userID, fmt.Sprintf("报销申请：%s", claim.Title)); err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("冻结预算失败：%v", err)
		}
	}

	// 启动工作流
	workflowReq := &dto.StartWorkflowRequest{
		DefinitionKey: "expense_claim_approval",
		BusinessID:    claim.ID,
		BusinessType:  "expense_claim",
		StartUserID:   userID,
		Variables: map[string]interface{}{
			"amount":       claim.TotalAmount,
			"departmentId": claim.DepartmentID,
			"title":        claim.Title,
		},
	}

	workflowResp, err := s.workflowService.StartWorkflow(workflowReq)
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("启动工作流失败：%v", err)
	}

	// 更新工作流实例ID
	instanceUUID, err := uuid.Parse(workflowResp.InstanceID)
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("解析工作流实例ID失败: %v", err)
	}
	claim.WorkflowInstanceID = &instanceUUID
	claim.Status = "pending"
	now := time.Now()
	claim.SubmittedAt = &now

	if err := tx.Save(claim).Error; err != nil {
		tx.Rollback()
		return nil, errors.New("更新报销单状态失败")
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, errors.New("提交事务失败")
	}

	// 返回结果
	return s.buildClaimResponse(claim), nil
}

// GetExpenseTypes 获取费用类型列表
func (s *ExpenseService) GetExpenseTypes() ([]dto.ExpenseTypeResponse, error) {
	var expenseTypes []models.ExpenseType
	if err := s.db.Where("status = ?", "active").Find(&expenseTypes).Error; err != nil {
		return nil, err
	}

	var result []dto.ExpenseTypeResponse
	for _, et := range expenseTypes {
		result = append(result, dto.ExpenseTypeResponse{
			ID:          et.ID,
			Code:        et.Code,
			Name:        et.Name,
			Description: et.Description,
			IsActive:    et.Status == "active",
		})
	}

	return result, nil
}

// SearchPreApprovals 搜索事前申请
func (s *ExpenseService) SearchPreApprovals(keyword string, userID uuid.UUID, limit int) ([]dto.PreApprovalSearchResult, error) {
	var preApprovals []models.PreApproval

	query := s.db.Model(&models.PreApproval{}).
		Where("title ILIKE ? AND status = ?", "%"+keyword+"%", "approved").
		Limit(limit)

	if err := query.Find(&preApprovals).Error; err != nil {
		return nil, err
	}

	var result []dto.PreApprovalSearchResult
	for _, pa := range preApprovals {
		result = append(result, dto.PreApprovalSearchResult{
			ID:     pa.ID,
			Title:  pa.Title,
			Amount: pa.Amount,
			Status: pa.Status,
		})
	}

	return result, nil
}

// SearchContracts 搜索合同
func (s *ExpenseService) SearchContracts(keyword string, userID uuid.UUID, limit int) ([]dto.ContractSearchResult, error) {
	var contracts []models.Contract

	query := s.db.Model(&models.Contract{}).
		Where("(name ILIKE ? OR contract_no ILIKE ?) AND status = ?", "%"+keyword+"%", "%"+keyword+"%", "active").
		Limit(limit)

	if err := query.Find(&contracts).Error; err != nil {
		return nil, err
	}

	var result []dto.ContractSearchResult
	for _, c := range contracts {
		result = append(result, dto.ContractSearchResult{
			ID:         c.ID,
			Name:       c.Name,
			ContractNo: c.ContractNo,
			Amount:     c.Amount,
			Status:     c.Status,
		})
	}

	return result, nil
}

// GetClaim 获取报销申请详情
func (s *ExpenseService) GetClaim(claimID uuid.UUID, userID uuid.UUID) (*dto.ExpenseClaimResponse, error) {
	var claim models.ExpenseClaim

	query := s.db.Preload("Applicant").
		Preload("Department").
		Preload("Details.ExpenseType").
		Preload("Details.BudgetItem").
		Preload("Attachments").
		Preload("RelatedPreApproval").
		Preload("RelatedContract")

	if err := query.First(&claim, "id = ?", claimID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("报销申请不存在")
		}
		return nil, err
	}

	// 权限检查：只能查看自己的报销申请或有审批权限
	if claim.ApplicantID != userID {
		// TODO: 检查是否有审批权限
	}

	return s.buildClaimResponse(&claim), nil
}

// GetClaimList 获取报销申请列表
func (s *ExpenseService) GetClaimList(req *dto.GetExpenseClaimListRequest, userID uuid.UUID) (*dto.PagedResponse, error) {
	var claims []models.ExpenseClaim
	var total int64

	query := s.db.Model(&models.ExpenseClaim{}).
		Preload("Applicant").
		Preload("Department")

	// 默认只查看自己的申请
	query = query.Where("applicant_id = ?", userID)

	// 状态筛选
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 关键词搜索
	if req.Keyword != "" {
		query = query.Where("title ILIKE ?", "%"+req.Keyword+"%")
	}

	// 时间范围筛选
	if req.StartDate != nil {
		query = query.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != nil {
		query = query.Where("created_at <= ?", req.EndDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).
		Order("created_at DESC").Find(&claims).Error; err != nil {
		return nil, err
	}

	// 构建响应
	var items []interface{}
	for _, claim := range claims {
		items = append(items, s.buildClaimResponse(&claim))
	}

	return &dto.PagedResponse{
		Items:    items,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// DeleteClaim 删除报销申请
func (s *ExpenseService) DeleteClaim(claimID uuid.UUID, userID uuid.UUID) error {
	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var claim models.ExpenseClaim
	if err := tx.Preload("Details").First(&claim, "id = ? AND applicant_id = ?", claimID, userID).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("报销申请不存在")
		}
		return err
	}

	// 检查状态
	if claim.Status != "draft" {
		tx.Rollback()
		return errors.New("只能删除草稿状态的报销申请")
	}

	// 释放冻结的预算
	for _, detail := range claim.Details {
		if err := s.budgetService.ReleaseBudget(tx, detail.BudgetItemID, claim.ID, "expense_claim"); err != nil {
			logger.Error("释放预算失败:", err)
			// 继续执行，不阻断删除操作
		}
	}

	// 删除报销申请
	if err := tx.Delete(&claim).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// WithdrawClaim 撤回报销申请
func (s *ExpenseService) WithdrawClaim(claimID uuid.UUID, userID uuid.UUID) error {
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var claim models.ExpenseClaim
	if err := tx.First(&claim, "id = ? AND applicant_id = ?", claimID, userID).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("报销申请不存在")
		}
		return err
	}

	if claim.Status != "pending" {
		tx.Rollback()
		return errors.New("只能撤回待审批状态的报销申请")
	}

	// 终止工作流
	if claim.WorkflowInstanceID != nil {
		if err := s.workflowService.TerminateWorkflow(claim.WorkflowInstanceID.String(), "申请人撤回"); err != nil {
			logger.Error("终止工作流失败:", err)
		}
	}

	// 更新状态
	claim.Status = "draft"
	claim.WorkflowInstanceID = nil
	claim.SubmittedAt = nil

	if err := tx.Save(&claim).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// HandleApprovalPassed 处理审批通过
func (s *ExpenseService) HandleApprovalPassed(businessID uuid.UUID) error {
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var claim models.ExpenseClaim
	if err := tx.Preload("Details").First(&claim, "id = ?", businessID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 更新状态
	claim.Status = "approved"
	now := time.Now()
	claim.ApprovedAt = &now

	if err := tx.Save(&claim).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 将冻结预算转为使用
	for _, detail := range claim.Details {
		if err := s.budgetService.UseBudget(tx, detail.BudgetItemID, detail.Amount,
			claim.ID, "expense_claim"); err != nil {
			tx.Rollback()
			return fmt.Errorf("使用预算失败：%v", err)
		}
	}

	return tx.Commit().Error
}

// HandleApprovalRejected 处理审批驳回
func (s *ExpenseService) HandleApprovalRejected(businessID uuid.UUID) error {
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var claim models.ExpenseClaim
	if err := tx.Preload("Details").First(&claim, "id = ?", businessID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 更新状态
	claim.Status = "rejected"

	if err := tx.Save(&claim).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 释放冻结的预算
	for _, detail := range claim.Details {
		if err := s.budgetService.ReleaseBudget(tx, detail.BudgetItemID, claim.ID, "expense_claim"); err != nil {
			logger.Error("释放预算失败:", err)
		}
	}

	return tx.Commit().Error
}

// buildClaimResponse 构建响应对象
func (s *ExpenseService) buildClaimResponse(claim *models.ExpenseClaim) *dto.ExpenseClaimResponse {
	resp := &dto.ExpenseClaimResponse{
		ID:                   claim.ID,
		Title:                claim.Title,
		ApplicantID:          claim.ApplicantID,
		DepartmentID:         claim.DepartmentID,
		TotalAmount:          claim.TotalAmount,
		Status:               claim.Status,
		PayeeType:            claim.PayeeType,
		RelatedPreApprovalID: claim.RelatedPreApprovalID,
		RelatedContractID:    claim.RelatedContractID,
		SubmittedAt:          claim.SubmittedAt,
		ApprovedAt:           claim.ApprovedAt,
		PaidAt:               claim.PaidAt,
		CreatedAt:            claim.CreatedAt,
		UpdatedAt:            claim.UpdatedAt,
	}

	// 处理WorkflowInstanceID
	if claim.WorkflowInstanceID != nil {
		instanceIDStr := claim.WorkflowInstanceID.String()
		resp.WorkflowInstanceID = &instanceIDStr
	}

	// 处理PayeeInfo
	if len(claim.PayeeInfo) > 0 {
		var payeeInfo dto.PayeeInfoResponse
		if err := json.Unmarshal(claim.PayeeInfo, &payeeInfo); err == nil {
			resp.PayeeInfo = payeeInfo
		}
	}

	if claim.Applicant != nil {
		resp.ApplicantName = claim.Applicant.Username
	}

	if claim.Department != nil {
		resp.DepartmentName = claim.Department.Name
	}

	// 处理明细
	for _, detail := range claim.Details {
		detailResp := dto.ExpenseClaimDetailResponse{
			ID:            detail.ID,
			ExpenseTypeID: detail.ExpenseTypeID,
			BudgetItemID:  detail.BudgetItemID,
			Amount:        detail.Amount,
			Description:   detail.Description,
		}

		// 处理InvoiceData
		if len(detail.InvoiceData) > 0 {
			var invoiceData dto.InvoiceInfoResponse
			if err := json.Unmarshal(detail.InvoiceData, &invoiceData); err == nil {
				detailResp.InvoiceData = invoiceData
			}
		}

		if detail.ExpenseType != nil {
			detailResp.ExpenseTypeName = detail.ExpenseType.Name
		}

		if detail.BudgetItem != nil {
			detailResp.BudgetItemName = detail.BudgetItem.SubjectName
		}

		resp.Details = append(resp.Details, detailResp)
	}

	return resp
}
