<template>
  <div class="approval-history">
    <div class="page-header">
      <h2>审批历史</h2>
      <a-space>
        <a-button @click="exportHistory">
          <ExportOutlined />
          导出
        </a-button>
        <a-button @click="refreshData">
          <ReloadOutlined />
          刷新
        </a-button>
      </a-space>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="总审批数"
              :value="statistics.totalApprovals"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="通过率"
              :value="statistics.approvalRate"
              suffix="%"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="平均处理时长"
              :value="statistics.avgProcessTime"
              suffix="小时"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="本月处理数"
              :value="statistics.monthlyCount"
              :value-style="{ color: '#fa8c16' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 高级筛选 -->
    <a-card title="筛选条件" class="filter-card">
      <a-form layout="inline" :model="filterForm">
        <a-form-item label="业务类型">
          <a-select v-model:value="filterForm.businessType" placeholder="选择业务类型" style="width: 150px" allowClear>
            <a-select-option value="expense_claim">报销申请</a-select-option>
            <a-select-option value="contract">合同审批</a-select-option>
            <a-select-option value="pre_approval">事前申请</a-select-option>
            <a-select-option value="purchase">采购申请</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="审批结果">
          <a-select v-model:value="filterForm.action" placeholder="选择审批结果" style="width: 120px" allowClear>
            <a-select-option value="approve">通过</a-select-option>
            <a-select-option value="reject">驳回</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="审批人">
          <a-input v-model:value="filterForm.approver" placeholder="输入审批人姓名" style="width: 150px" />
        </a-form-item>
        
        <a-form-item label="申请人">
          <a-input v-model:value="filterForm.applicant" placeholder="输入申请人姓名" style="width: 150px" />
        </a-form-item>
        
        <a-form-item label="时间范围">
          <a-range-picker 
            v-model:value="filterForm.dateRange" 
            :placeholder="['开始日期', '结束日期']"
            style="width: 240px"
          />
        </a-form-item>
        
        <a-form-item label="金额范围">
          <a-input-group compact>
            <a-input-number 
              v-model:value="filterForm.minAmount" 
              placeholder="最小金额" 
              style="width: 100px"
              :min="0"
            />
            <a-input
              style="width: 30px; border-left: 0; border-right: 0; pointer-events: none"
              placeholder="~"
              disabled
            />
            <a-input-number 
              v-model:value="filterForm.maxAmount" 
              placeholder="最大金额" 
              style="width: 100px; border-left: 0"
              :min="0"
            />
          </a-input-group>
        </a-form-item>
        
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="searchHistory">搜索</a-button>
            <a-button @click="resetFilter">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 审批历史列表 -->
    <a-card title="审批记录" class="history-list-card">
      <a-table
        :columns="historyColumns"
        :data-source="approvalHistoryList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'businessType'">
            <a-tag>{{ getBusinessTypeText(record.businessType) }}</a-tag>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-tag :color="getActionColor(record.action)">
              {{ getActionText(record.action) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'amount'">
            <span v-if="record.amount">¥{{ record.amount.toFixed(2) }}</span>
            <span v-else>-</span>
          </template>
          
          <template v-if="column.key === 'duration'">
            {{ record.duration || '-' }}
          </template>
          
          <template v-if="column.key === 'createdAt'">
            {{ formatTime(record.createdAt) }}
          </template>
          
          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button size="small" @click="viewDetail(record)">查看详情</a-button>
              <a-button size="small" @click="viewWorkflow(record)">查看流程</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="审批详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentRecord">
        <a-descriptions title="基本信息" :column="2" bordered>
          <a-descriptions-item label="业务类型">
            {{ getBusinessTypeText(currentRecord.businessType) }}
          </a-descriptions-item>
          <a-descriptions-item label="申请标题">
            {{ currentRecord.title }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人">
            {{ currentRecord.applicantName }}
          </a-descriptions-item>
          <a-descriptions-item label="审批人">
            {{ currentRecord.approverName }}
          </a-descriptions-item>
          <a-descriptions-item label="审批节点">
            {{ currentRecord.nodeName }}
          </a-descriptions-item>
          <a-descriptions-item label="审批结果">
            <a-tag :color="getActionColor(currentRecord.action)">
              {{ getActionText(currentRecord.action) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="申请金额">
            <span v-if="currentRecord.amount">¥{{ currentRecord.amount.toFixed(2) }}</span>
            <span v-else>-</span>
          </a-descriptions-item>
          <a-descriptions-item label="处理时长">
            {{ currentRecord.duration || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="审批时间">
            {{ formatTime(currentRecord.createdAt) }}
          </a-descriptions-item>
        </a-descriptions>
        
        <div v-if="currentRecord.comment" style="margin-top: 16px;">
          <h4>审批意见</h4>
          <div class="approval-comment">
            {{ currentRecord.comment }}
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 工作流弹窗 -->
    <a-modal
      v-model:visible="workflowModalVisible"
      title="审批流程"
      width="1000px"
      :footer="null"
    >
      <WorkflowChart
        v-if="currentRecord"
        :instance-id="currentRecord.instanceId"
        :show-history="true"
        title=""
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { ExportOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import WorkflowChart from '@/components/WorkflowChart.vue'
import type { ApprovalHistory } from '@/types/workflow'

// 响应式数据
const loading = ref(false)
const detailModalVisible = ref(false)
const workflowModalVisible = ref(false)
const approvalHistoryList = ref<ApprovalHistory[]>([])
const currentRecord = ref<ApprovalHistory | null>(null)

// 筛选表单
const filterForm = reactive({
  businessType: undefined,
  action: undefined,
  approver: '',
  applicant: '',
  dateRange: undefined,
  minAmount: undefined,
  maxAmount: undefined
})

// 统计数据
const statistics = reactive({
  totalApprovals: 156,
  approvalRate: 85.2,
  avgProcessTime: 3.2,
  monthlyCount: 42
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列定义
const historyColumns = [
  { title: '业务类型', key: 'businessType', width: 100 },
  { title: '申请标题', dataIndex: 'title', key: 'title', width: 200, ellipsis: true },
  { title: '申请人', dataIndex: 'applicantName', key: 'applicantName', width: 100 },
  { title: '审批人', dataIndex: 'approverName', key: 'approverName', width: 100 },
  { title: '审批节点', dataIndex: 'nodeName', key: 'nodeName', width: 120 },
  { title: '审批结果', key: 'action', width: 100 },
  { title: '申请金额', key: 'amount', width: 120 },
  { title: '处理时长', key: 'duration', width: 100 },
  { title: '审批时间', key: 'createdAt', width: 150 },
  { title: '操作', key: 'actions', width: 150, fixed: 'right' }
]

// 获取业务类型文本
const getBusinessTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    expense_claim: '报销申请',
    contract: '合同审批',
    pre_approval: '事前申请',
    purchase: '采购申请'
  }
  return textMap[type] || type
}

// 获取动作颜色
const getActionColor = (action: string) => {
  const colorMap: Record<string, string> = {
    start: 'blue',
    approve: 'green',
    reject: 'red'
  }
  return colorMap[action] || 'default'
}

// 获取动作文本
const getActionText = (action: string) => {
  const textMap: Record<string, string> = {
    start: '发起申请',
    approve: '审批通过',
    reject: '审批驳回'
  }
  return textMap[action] || action
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

// 加载审批历史
const loadApprovalHistory = async () => {
  loading.value = true
  try {
    // 模拟数据，实际应该调用API
    const mockData: ApprovalHistory[] = [
      {
        id: '1',
        instanceId: 'instance1',
        nodeId: 'dept_head',
        nodeName: '部门负责人审批',
        approverId: 'user1',
        approverName: '张三',
        action: 'approve',
        comment: '同意申请，金额合理',
        createdAt: new Date(Date.now() - 3600000).toISOString(),
        title: '办公用品采购报销',
        applicantName: '李四',
        businessType: 'expense_claim',
        amount: 1500.00,
        duration: '2小时'
      },
      {
        id: '2',
        instanceId: 'instance2',
        nodeId: 'finance',
        nodeName: '财务审核',
        approverId: 'user2',
        approverName: '王五',
        action: 'reject',
        comment: '发票信息不完整，请补充',
        createdAt: new Date(Date.now() - 7200000).toISOString(),
        title: '差旅费报销申请',
        applicantName: '赵六',
        businessType: 'expense_claim',
        amount: 3200.00,
        duration: '1.5小时'
      },
      {
        id: '3',
        instanceId: 'instance3',
        nodeId: 'general_manager',
        nodeName: '总经理审批',
        approverId: 'user3',
        approverName: '陈七',
        action: 'approve',
        comment: '合同条款合理，同意签署',
        createdAt: new Date(Date.now() - 86400000).toISOString(),
        title: '设备采购合同',
        applicantName: '孙八',
        businessType: 'contract',
        amount: 50000.00,
        duration: '4小时'
      }
    ]

    approvalHistoryList.value = mockData
    pagination.total = mockData.length
  } catch (error) {
    console.error('加载审批历史失败:', error)
    message.error('加载审批历史失败')
  } finally {
    loading.value = false
  }
}

// 搜索历史
const searchHistory = () => {
  pagination.current = 1
  loadApprovalHistory()
}

// 重置筛选
const resetFilter = () => {
  Object.assign(filterForm, {
    businessType: undefined,
    action: undefined,
    approver: '',
    applicant: '',
    dateRange: undefined,
    minAmount: undefined,
    maxAmount: undefined
  })
  searchHistory()
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadApprovalHistory()
}

// 查看详情
const viewDetail = (record: ApprovalHistory) => {
  currentRecord.value = record
  detailModalVisible.value = true
}

// 查看工作流
const viewWorkflow = (record: ApprovalHistory) => {
  currentRecord.value = record
  workflowModalVisible.value = true
}

// 导出历史
const exportHistory = () => {
  message.info('导出功能开发中...')
}

// 刷新数据
const refreshData = () => {
  loadApprovalHistory()
}

// 初始化
onMounted(() => {
  loadApprovalHistory()
})
</script>

<style scoped>
.approval-history {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.statistics-cards {
  margin-bottom: 24px;
}

.filter-card {
  margin-bottom: 16px;
}

.history-list-card {
  margin-bottom: 24px;
}

.approval-comment {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
}
</style>
</script>
