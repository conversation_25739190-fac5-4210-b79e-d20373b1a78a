package main

import (
	"fmt"
	"log"

	"hospital-management/pkg/logger"
)

func main() {
	// 初始化日志
	if err := logger.Init("debug"); err != nil {
		log.Fatal("初始化日志失败:", err)
	}

	fmt.Println("编译测试成功！")
	fmt.Println("所有服务和控制器都可以正常创建")

	fmt.Println("\n模块二：支出控制后端设计完成！")
	fmt.Println("包含以下功能：")
	fmt.Println("- 报销申请管理")
	fmt.Println("- 预算控制")
	fmt.Println("- 工作流审批")
	fmt.Println("- 费用类型管理")
	fmt.Println("- 前端API对接")
}
