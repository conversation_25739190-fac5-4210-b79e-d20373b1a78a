package dto

import (
	"time"

	"github.com/google/uuid"
)

// CreateExpenseClaimRequest 创建报销申请请求
type CreateExpenseClaimRequest struct {
	Title                string                            `json:"title" binding:"required" validate:"min=1,max=200"`
	PayeeType            string                            `json:"payeeType" binding:"required" validate:"oneof=personal corporate"`
	PayeeInfo            PayeeInfoRequest                  `json:"payeeInfo" binding:"required"`
	RelatedPreApprovalID *uuid.UUID                        `json:"relatedPreApprovalId"`
	RelatedContractID    *uuid.UUID                        `json:"relatedContractId"`
	Details              []CreateExpenseClaimDetailRequest `json:"details" binding:"required,min=1"`
	Attachments          []AttachmentRequest               `json:"attachments"`
}

// CreateExpenseClaimDetailRequest 创建报销明细请求
type CreateExpenseClaimDetailRequest struct {
	ExpenseTypeID uuid.UUID          `json:"expenseTypeId" binding:"required"`
	BudgetItemID  uuid.UUID          `json:"budgetItemId" binding:"required"`
	Amount        float64            `json:"amount" binding:"required,gt=0"`
	Description   string             `json:"description" validate:"max=500"`
	InvoiceData   InvoiceInfoRequest `json:"invoiceData"`
}

// PayeeInfoRequest 收款人信息请求
type PayeeInfoRequest struct {
	Type      string `json:"type" binding:"required" validate:"oneof=personal corporate"`
	Name      string `json:"name" binding:"required" validate:"min=1,max=100"`
	BankName  string `json:"bankName" binding:"required" validate:"min=1,max=100"`
	AccountNo string `json:"accountNo" binding:"required" validate:"min=1,max=50"`
	TaxNo     string `json:"taxNo" validate:"max=50"`
	Address   string `json:"address" validate:"max=200"`
	Phone     string `json:"phone" validate:"max=20"`
}

// InvoiceInfoRequest 发票信息请求
type InvoiceInfoRequest struct {
	Code        string    `json:"code" validate:"max=20"`
	Number      string    `json:"number" validate:"max=20"`
	Date        time.Time `json:"date"`
	Amount      float64   `json:"amount" validate:"gte=0"`
	SellerName  string    `json:"sellerName" validate:"max=100"`
	SellerTaxNo string    `json:"sellerTaxNo" validate:"max=50"`
	BuyerName   string    `json:"buyerName" validate:"max=100"`
	BuyerTaxNo  string    `json:"buyerTaxNo" validate:"max=50"`
	Verified    bool      `json:"verified"`
}

// AttachmentRequest 附件请求
type AttachmentRequest struct {
	FileName     string `json:"fileName" binding:"required"`
	OriginalName string `json:"originalName" binding:"required"`
	FilePath     string `json:"filePath" binding:"required"`
	FileSize     int64  `json:"fileSize"`
	FileType     string `json:"fileType"`
}

// GetExpenseClaimListRequest 获取报销申请列表请求
type GetExpenseClaimListRequest struct {
	PaginationRequest
	Status    string     `form:"status" validate:"omitempty,oneof=draft pending approved rejected paid"`
	Keyword   string     `form:"keyword" validate:"max=100"`
	StartDate *time.Time `form:"startDate"`
	EndDate   *time.Time `form:"endDate"`
}

// ExpenseClaimResponse 报销申请响应
type ExpenseClaimResponse struct {
	ID                   uuid.UUID                        `json:"id"`
	Title                string                           `json:"title"`
	ApplicantID          uuid.UUID                        `json:"applicantId"`
	ApplicantName        string                           `json:"applicantName"`
	DepartmentID         uuid.UUID                        `json:"departmentId"`
	DepartmentName       string                           `json:"departmentName"`
	TotalAmount          float64                          `json:"totalAmount"`
	Status               string                           `json:"status"`
	PayeeType            string                           `json:"payeeType"`
	PayeeInfo            PayeeInfoResponse                `json:"payeeInfo"`
	WorkflowInstanceID   *string                          `json:"workflowInstanceId"`
	RelatedPreApprovalID *uuid.UUID                       `json:"relatedPreApprovalId"`
	RelatedContractID    *uuid.UUID                       `json:"relatedContractId"`
	SubmittedAt          *time.Time                       `json:"submittedAt"`
	ApprovedAt           *time.Time                       `json:"approvedAt"`
	PaidAt               *time.Time                       `json:"paidAt"`
	Details              []ExpenseClaimDetailResponse     `json:"details"`
	Attachments          []ExpenseClaimAttachmentResponse `json:"attachments"`
	CreatedAt            time.Time                        `json:"createdAt"`
	UpdatedAt            time.Time                        `json:"updatedAt"`
}

// ExpenseClaimDetailResponse 报销明细响应
type ExpenseClaimDetailResponse struct {
	ID              uuid.UUID           `json:"id"`
	ExpenseTypeID   uuid.UUID           `json:"expenseTypeId"`
	ExpenseTypeName string              `json:"expenseTypeName"`
	BudgetItemID    uuid.UUID           `json:"budgetItemId"`
	BudgetItemName  string              `json:"budgetItemName"`
	Amount          float64             `json:"amount"`
	Description     string              `json:"description"`
	InvoiceData     InvoiceInfoResponse `json:"invoiceData"`
}

// PayeeInfoResponse 收款人信息响应
type PayeeInfoResponse struct {
	Type      string `json:"type"`
	Name      string `json:"name"`
	BankName  string `json:"bankName"`
	AccountNo string `json:"accountNo"`
	TaxNo     string `json:"taxNo"`
	Address   string `json:"address"`
	Phone     string `json:"phone"`
}

// InvoiceInfoResponse 发票信息响应
type InvoiceInfoResponse struct {
	Code        string    `json:"code"`
	Number      string    `json:"number"`
	Date        time.Time `json:"date"`
	Amount      float64   `json:"amount"`
	SellerName  string    `json:"sellerName"`
	SellerTaxNo string    `json:"sellerTaxNo"`
	BuyerName   string    `json:"buyerName"`
	BuyerTaxNo  string    `json:"buyerTaxNo"`
	Verified    bool      `json:"verified"`
}

// ExpenseClaimAttachmentResponse 报销附件响应
type ExpenseClaimAttachmentResponse struct {
	ID           uuid.UUID `json:"id"`
	FileName     string    `json:"fileName"`
	OriginalName string    `json:"originalName"`
	FilePath     string    `json:"filePath"`
	FileSize     int64     `json:"fileSize"`
	FileType     string    `json:"fileType"`
	UploadedBy   uuid.UUID `json:"uploadedBy"`
	CreatedAt    time.Time `json:"createdAt"`
}

// SaveDraftRequest 保存草稿请求
type SaveDraftRequest struct {
	CreateExpenseClaimRequest
	Status string `json:"status" binding:"required" validate:"eq=draft"`
}

// UpdateExpenseClaimRequest 更新报销申请请求
type UpdateExpenseClaimRequest struct {
	Title                string                            `json:"title" binding:"required" validate:"min=1,max=200"`
	PayeeType            string                            `json:"payeeType" binding:"required" validate:"oneof=personal corporate"`
	PayeeInfo            PayeeInfoRequest                  `json:"payeeInfo" binding:"required"`
	RelatedPreApprovalID *uuid.UUID                        `json:"relatedPreApprovalId"`
	RelatedContractID    *uuid.UUID                        `json:"relatedContractId"`
	Details              []CreateExpenseClaimDetailRequest `json:"details" binding:"required,min=1"`
	Attachments          []AttachmentRequest               `json:"attachments"`
}

// GetBudgetBalanceRequest 获取预算余额请求
type GetBudgetBalanceRequest struct {
	BudgetItemID uuid.UUID `uri:"budgetItemId" binding:"required"`
}

// GetBudgetBalanceResponse 获取预算余额响应
type GetBudgetBalanceResponse struct {
	BudgetItemID     uuid.UUID `json:"budgetItemId"`
	BudgetItemName   string    `json:"budgetItemName"`
	TotalAmount      float64   `json:"totalAmount"`
	UsedAmount       float64   `json:"usedAmount"`
	FrozenAmount     float64   `json:"frozenAmount"`
	AvailableBalance float64   `json:"availableBalance"`
	ControlType      string    `json:"controlType"`
}

// SearchPreApprovalsRequest 搜索事前申请请求
type SearchPreApprovalsRequest struct {
	Keyword string `form:"keyword" binding:"required" validate:"min=1"`
	Limit   int    `form:"limit" validate:"omitempty,min=1,max=50"`
}

// SearchContractsRequest 搜索合同请求
type SearchContractsRequest struct {
	Keyword string `form:"keyword" binding:"required" validate:"min=1"`
	Limit   int    `form:"limit" validate:"omitempty,min=1,max=50"`
}

// PreApprovalSearchResult 事前申请搜索结果
type PreApprovalSearchResult struct {
	ID     uuid.UUID `json:"id"`
	Title  string    `json:"title"`
	Amount float64   `json:"amount"`
	Status string    `json:"status"`
}

// ContractSearchResult 合同搜索结果
type ContractSearchResult struct {
	ID         uuid.UUID `json:"id"`
	Name       string    `json:"name"`
	ContractNo string    `json:"contractNo"`
	Amount     float64   `json:"amount"`
	Status     string    `json:"status"`
}

// ExpenseTypeResponse 费用类型响应
type ExpenseTypeResponse struct {
	ID          uuid.UUID `json:"id"`
	Code        string    `json:"code"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	IsActive    bool      `json:"isActive"`
}

// BudgetItemResponse 预算科目响应
type BudgetItemResponse struct {
	ID            uuid.UUID  `json:"id"`
	Year          int        `json:"year"`
	SubjectCode   string     `json:"subjectCode"`
	SubjectName   string     `json:"subjectName"`
	TotalAmount   float64    `json:"totalAmount"`
	UsedAmount    float64    `json:"usedAmount"`
	FrozenAmount  float64    `json:"frozenAmount"`
	ControlType   string     `json:"controlType"`
	ExpenseTypeID *uuid.UUID `json:"expenseTypeId"`
}

// ExpenseStatisticsResponse 报销统计响应
type ExpenseStatisticsResponse struct {
	TotalAmount     float64 `json:"totalAmount"`
	PendingCount    int64   `json:"pendingCount"`
	ApprovedCount   int64   `json:"approvedCount"`
	RejectedCount   int64   `json:"rejectedCount"`
	DraftCount      int64   `json:"draftCount"`
	PaidCount       int64   `json:"paidCount"`
	ThisMonthAmount float64 `json:"thisMonthAmount"`
	LastMonthAmount float64 `json:"lastMonthAmount"`
}

// BatchDeleteRequest 批量删除请求
type BatchDeleteRequest struct {
	IDs []uuid.UUID `json:"ids" binding:"required,min=1"`
}

// ExportExpenseClaimsRequest 导出报销申请请求
type ExportExpenseClaimsRequest struct {
	Status    string     `form:"status"`
	StartDate *time.Time `form:"startDate"`
	EndDate   *time.Time `form:"endDate"`
	Format    string     `form:"format" validate:"omitempty,oneof=xlsx pdf"`
}
