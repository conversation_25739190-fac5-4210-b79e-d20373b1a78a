package services

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
)

type WorkflowService struct {
	db             *gorm.DB
	expenseService *ExpenseService // 避免循环依赖，这里使用接口或者回调函数
}

func NewWorkflowService(db *gorm.DB) *WorkflowService {
	return &WorkflowService{
		db: db,
	}
}

// SetExpenseService 设置费用服务（避免循环依赖）
func (s *WorkflowService) SetExpenseService(expenseService *ExpenseService) {
	s.expenseService = expenseService
}

// StartWorkflow 启动工作流
func (s *WorkflowService) StartWorkflow(req *dto.StartWorkflowRequest) (*dto.StartWorkflowResponse, error) {
	// 查找工作流定义
	var definition models.WorkflowDefinition
	if err := s.db.Preload("Nodes").First(&definition, "key = ? AND status = ?", req.DefinitionKey, "active").Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("工作流定义不存在: %s", req.DefinitionKey)
		}
		return nil, fmt.Errorf("查询工作流定义失败: %v", err)
	}

	// 找到开始节点
	var startNode *models.WorkflowNode
	for _, node := range definition.Nodes {
		if node.NodeType == "start" {
			startNode = &node
			break
		}
	}

	if startNode == nil {
		return nil, errors.New("工作流定义中未找到开始节点")
	}

	// 确定下一个节点
	nextNodeID := s.getNextNodeID(startNode.NextNodes, req.Variables)
	if nextNodeID == "" {
		return nil, errors.New("无法确定下一个审批节点")
	}

	// 创建工作流实例
	instance := models.WorkflowInstance{
		BaseModel: models.BaseModel{
			ID: uuid.New(),
		},
		DefinitionKey: req.DefinitionKey,
		BusinessID:    req.BusinessID,
		BusinessType:  req.BusinessType,
		CurrentNodeID: nextNodeID,
		Status:        "active",
		StartUserID:   req.StartUserID,
		StartTime:     time.Now(),
	}

	if err := s.db.Create(&instance).Error; err != nil {
		return nil, fmt.Errorf("创建工作流实例失败: %v", err)
	}

	// 记录开始节点的历史
	history := models.WorkflowApprovalHistory{
		BaseModel: models.BaseModel{
			ID: uuid.New(),
		},
		InstanceID: instance.ID,
		NodeID:     startNode.NodeID,
		NodeName:   startNode.NodeName,
		ApproverID: req.StartUserID,
		Action:     "start",
		Comment:    "发起申请",
	}

	if err := s.db.Create(&history).Error; err != nil {
		logger.Error("记录工作流历史失败:", err)
	}

	logger.Info(fmt.Sprintf("工作流启动成功 - 实例ID: %s, 业务ID: %s", instance.ID, req.BusinessID))

	return &dto.StartWorkflowResponse{
		InstanceID:    instance.ID.String(),
		CurrentNodeID: nextNodeID,
		Status:        "active",
	}, nil
}

// TerminateWorkflow 终止工作流
func (s *WorkflowService) TerminateWorkflow(instanceID string, reason string) error {
	instanceUUID, err := uuid.Parse(instanceID)
	if err != nil {
		return fmt.Errorf("无效的实例ID: %v", err)
	}

	var instance models.WorkflowInstance
	if err := s.db.First(&instance, "id = ?", instanceUUID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("工作流实例不存在")
		}
		return fmt.Errorf("查询工作流实例失败: %v", err)
	}

	// 更新实例状态
	now := time.Now()
	updates := map[string]interface{}{
		"status":   "terminated",
		"end_time": &now,
	}

	if err := s.db.Model(&instance).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新工作流实例状态失败: %v", err)
	}

	logger.Info(fmt.Sprintf("工作流终止成功 - 实例ID: %s, 原因: %s", instanceID, reason))
	return nil
}

// ProcessApproval 处理审批
func (s *WorkflowService) ProcessApproval(req *dto.ProcessApprovalRequest, approverID uuid.UUID) error {
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询工作流实例
	var instance models.WorkflowInstance
	if err := tx.First(&instance, "id = ?", req.InstanceID).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("工作流实例不存在")
		}
		return fmt.Errorf("查询工作流实例失败: %v", err)
	}

	if instance.Status != "active" {
		tx.Rollback()
		return errors.New("工作流实例已结束，无法审批")
	}

	// 查询工作流定义和当前节点
	var definition models.WorkflowDefinition
	if err := tx.Preload("Nodes").First(&definition, "key = ?", instance.DefinitionKey).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("查询工作流定义失败: %v", err)
	}

	var currentNode *models.WorkflowNode
	for _, node := range definition.Nodes {
		if node.NodeID == instance.CurrentNodeID {
			currentNode = &node
			break
		}
	}

	if currentNode == nil {
		tx.Rollback()
		return errors.New("当前节点不存在")
	}

	// 验证审批权限
	if !s.canApprove(approverID, currentNode.Assignee) {
		tx.Rollback()
		return errors.New("您没有权限审批此节点")
	}

	// 记录审批历史
	history := models.WorkflowApprovalHistory{
		BaseModel: models.BaseModel{
			ID: uuid.New(),
		},
		InstanceID: instance.ID,
		NodeID:     currentNode.NodeID,
		NodeName:   currentNode.NodeName,
		ApproverID: approverID,
		Action:     req.Action,
		Comment:    req.Comment,
	}

	if err := tx.Create(&history).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("记录审批历史失败: %v", err)
	}

	// 处理审批结果
	if req.Action == "approve" {
		// 同意：移动到下一节点
		nextNodeID := s.getNextNodeID(currentNode.NextNodes, nil)
		if nextNodeID == "" || s.isEndNode(nextNodeID, definition.Nodes) {
			// 到达结束节点，流程完成
			now := time.Now()
			updates := map[string]interface{}{
				"status":   "completed",
				"end_time": &now,
			}
			if err := tx.Model(&instance).Updates(updates).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("更新工作流实例状态失败: %v", err)
			}

			// 触发业务回调
			if err := s.triggerBusinessCallback(instance.BusinessType, instance.BusinessID, "approved"); err != nil {
				tx.Rollback()
				return fmt.Errorf("触发业务回调失败: %v", err)
			}
		} else {
			// 移动到下一节点
			if err := tx.Model(&instance).Update("current_node_id", nextNodeID).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("更新当前节点失败: %v", err)
			}
		}
	} else if req.Action == "reject" {
		// 驳回：结束流程
		now := time.Now()
		updates := map[string]interface{}{
			"status":   "rejected",
			"end_time": &now,
		}
		if err := tx.Model(&instance).Updates(updates).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("更新工作流实例状态失败: %v", err)
		}

		// 触发业务回调
		if err := s.triggerBusinessCallback(instance.BusinessType, instance.BusinessID, "rejected"); err != nil {
			tx.Rollback()
			return fmt.Errorf("触发业务回调失败: %v", err)
		}
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	logger.Info(fmt.Sprintf("审批处理成功 - 实例ID: %s, 动作: %s, 审批人: %s", req.InstanceID, req.Action, approverID))
	return nil
}

// GetWorkflowInstance 获取工作流实例详情
func (s *WorkflowService) GetWorkflowInstance(instanceID uuid.UUID) (*dto.WorkflowInstanceResponse, error) {
	var instance models.WorkflowInstance
	if err := s.db.Preload("History.Approver").First(&instance, "id = ?", instanceID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("工作流实例不存在")
		}
		return nil, err
	}

	// 查询工作流定义
	var definition models.WorkflowDefinition
	if err := s.db.Preload("Nodes").First(&definition, "key = ?", instance.DefinitionKey).Error; err != nil {
		return nil, fmt.Errorf("查询工作流定义失败: %v", err)
	}

	// 构建节点信息
	var nodes []dto.WorkflowNodeResponse
	var edges []dto.WorkflowEdgeResponse

	for _, node := range definition.Nodes {
		status := "pending"
		assignee := ""

		// 确定节点状态
		for _, history := range instance.History {
			if history.NodeID == node.NodeID {
				if history.Action == "approve" || history.Action == "start" {
					status = "completed"
				} else if history.Action == "reject" {
					status = "rejected"
				}
				if history.Approver != nil {
					assignee = history.Approver.Username
				}
				break
			}
		}

		if node.NodeID == instance.CurrentNodeID && instance.Status == "active" {
			status = "processing"
		}

		nodes = append(nodes, dto.WorkflowNodeResponse{
			ID:       node.NodeID,
			Label:    node.NodeName,
			Status:   status,
			Assignee: assignee,
		})

		// 构建边
		if node.NextNodes != "" {
			nextNodes := strings.Split(node.NextNodes, ",")
			for _, nextNode := range nextNodes {
				nextNode = strings.TrimSpace(nextNode)
				if nextNode != "" {
					edges = append(edges, dto.WorkflowEdgeResponse{
						Source: node.NodeID,
						Target: nextNode,
					})
				}
			}
		}
	}

	return &dto.WorkflowInstanceResponse{
		ID:            instance.ID,
		DefinitionKey: instance.DefinitionKey,
		BusinessID:    instance.BusinessID,
		BusinessType:  instance.BusinessType,
		Status:        instance.Status,
		StartTime:     instance.StartTime,
		EndTime:       instance.EndTime,
		Nodes:         nodes,
		Edges:         edges,
	}, nil
}

// 辅助方法
func (s *WorkflowService) getNextNodeID(nextNodes string, variables map[string]interface{}) string {
	if nextNodes == "" {
		return ""
	}

	// 简化处理，取第一个节点
	// 实际应用中可能需要根据条件选择不同的节点
	nodes := strings.Split(nextNodes, ",")
	if len(nodes) > 0 {
		return strings.TrimSpace(nodes[0])
	}

	return ""
}

func (s *WorkflowService) isEndNode(nodeID string, nodes []models.WorkflowNode) bool {
	for _, node := range nodes {
		if node.NodeID == nodeID && node.NodeType == "end" {
			return true
		}
	}
	return false
}

func (s *WorkflowService) canApprove(approverID uuid.UUID, assignee string) bool {
	// 简化权限检查
	// 实际应用中需要根据assignee字段的格式进行复杂的权限验证
	// 例如：user:uuid, role:role_name, department:dept_id 等
	return true
}

func (s *WorkflowService) triggerBusinessCallback(businessType string, businessID uuid.UUID, action string) error {
	switch businessType {
	case "expense_claim":
		if s.expenseService != nil {
			if action == "approved" {
				return s.expenseService.HandleApprovalPassed(businessID)
			} else if action == "rejected" {
				return s.expenseService.HandleApprovalRejected(businessID)
			}
		}
	default:
		logger.Warn(fmt.Sprintf("未知的业务类型: %s", businessType))
	}
	return nil
}
