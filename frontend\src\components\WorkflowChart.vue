<template>
  <div class="workflow-chart">
    <div class="chart-header">
      <h3>{{ title }}</h3>
      <a-space>
        <a-button v-if="showPreview" @click="showWorkflowPreview">预览流程</a-button>
        <a-button v-if="instanceId" @click="refreshWorkflow">刷新</a-button>
      </a-space>
    </div>
    
    <div class="chart-container" ref="chartContainer">
      <div class="workflow-nodes">
        <div
          v-for="node in nodes"
          :key="node.id"
          :class="['workflow-node', `node-${node.status}`]"
          :style="getNodeStyle(node)"
        >
          <div class="node-icon">
            <CheckCircleOutlined v-if="node.status === 'completed'" />
            <ClockCircleOutlined v-if="node.status === 'processing'" />
            <ExclamationCircleOutlined v-if="node.status === 'rejected'" />
            <MinusCircleOutlined v-if="node.status === 'pending'" />
          </div>
          <div class="node-content">
            <div class="node-title">{{ node.label }}</div>
            <div class="node-assignee" v-if="node.assignee">{{ node.assignee }}</div>
          </div>
        </div>
      </div>
      
      <svg class="workflow-edges" :width="chartWidth" :height="chartHeight">
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
          </marker>
        </defs>
        <path
          v-for="edge in processedEdges"
          :key="`${edge.source}-${edge.target}`"
          :d="edge.path"
          stroke="#666"
          stroke-width="2"
          fill="none"
          marker-end="url(#arrowhead)"
        />
      </svg>
    </div>

    <!-- 审批历史 -->
    <div v-if="showHistory && approvalHistory.length > 0" class="approval-history">
      <a-divider>审批历史</a-divider>
      <a-timeline>
        <a-timeline-item
          v-for="history in approvalHistory"
          :key="history.id"
          :color="getHistoryColor(history.action)"
        >
          <template #dot>
            <CheckCircleOutlined v-if="history.action === 'approve'" style="color: #52c41a" />
            <CloseCircleOutlined v-if="history.action === 'reject'" style="color: #ff4d4f" />
            <PlayCircleOutlined v-if="history.action === 'start'" style="color: #1890ff" />
          </template>
          <div class="history-item">
            <div class="history-header">
              <span class="history-node">{{ history.nodeName }}</span>
              <span class="history-time">{{ formatTime(history.createdAt) }}</span>
            </div>
            <div class="history-content">
              <span class="history-approver">{{ history.approverName }}</span>
              <span class="history-action">{{ getActionText(history.action) }}</span>
            </div>
            <div v-if="history.comment" class="history-comment">
              {{ history.comment }}
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  MinusCircleOutlined,
  CloseCircleOutlined,
  PlayCircleOutlined
} from '@ant-design/icons-vue'
import { workflowApi } from '@/api/workflow'
import type { WorkflowNode, WorkflowEdge, ApprovalHistory } from '@/types/workflow'

// Props
interface Props {
  title?: string
  instanceId?: string
  businessType?: string
  businessAmount?: number
  departmentId?: string
  showPreview?: boolean
  showHistory?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '审批流程',
  showPreview: false,
  showHistory: true
})

// 响应式数据
const chartContainer = ref<HTMLElement>()
const nodes = ref<WorkflowNode[]>([])
const edges = ref<WorkflowEdge[]>([])
const approvalHistory = ref<ApprovalHistory[]>([])
const chartWidth = ref(800)
const chartHeight = ref(200)

// 计算属性
const processedEdges = computed(() => {
  return edges.value.map(edge => {
    const sourceNode = nodes.value.find(n => n.id === edge.source)
    const targetNode = nodes.value.find(n => n.id === edge.target)
    
    if (!sourceNode || !targetNode) return { ...edge, path: '' }
    
    const sourcePos = getNodePosition(sourceNode)
    const targetPos = getNodePosition(targetNode)
    
    // 简单的直线连接
    const path = `M ${sourcePos.x + 60} ${sourcePos.y + 30} L ${targetPos.x} ${targetPos.y + 30}`
    
    return { ...edge, path }
  })
})

// 获取节点位置
const getNodePosition = (node: WorkflowNode) => {
  const index = nodes.value.findIndex(n => n.id === node.id)
  return {
    x: index * 180 + 20,
    y: 50
  }
}

// 获取节点样式
const getNodeStyle = (node: WorkflowNode) => {
  const position = getNodePosition(node)
  return {
    left: `${position.x}px`,
    top: `${position.y}px`
  }
}

// 获取历史记录颜色
const getHistoryColor = (action: string) => {
  const colorMap: Record<string, string> = {
    start: 'blue',
    approve: 'green',
    reject: 'red'
  }
  return colorMap[action] || 'gray'
}

// 获取动作文本
const getActionText = (action: string) => {
  const textMap: Record<string, string> = {
    start: '发起申请',
    approve: '审批通过',
    reject: '审批驳回'
  }
  return textMap[action] || action
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

// 显示工作流预览
const showWorkflowPreview = async () => {
  if (!props.businessType) return
  
  try {
    const response = await workflowApi.getWorkflowPreview({
      type: props.businessType,
      amount: props.businessAmount,
      departmentId: props.departmentId
    })
    
    nodes.value = response.nodes
    edges.value = response.edges
    
    await nextTick()
    updateChartSize()
  } catch (error) {
    console.error('获取工作流预览失败:', error)
  }
}

// 刷新工作流
const refreshWorkflow = async () => {
  if (!props.instanceId) return
  
  try {
    const response = await workflowApi.getWorkflowInstance(props.instanceId)
    
    nodes.value = response.nodes
    edges.value = response.edges
    
    // 如果需要显示历史，这里可以加载历史数据
    // approvalHistory.value = response.history || []
    
    await nextTick()
    updateChartSize()
  } catch (error) {
    console.error('获取工作流实例失败:', error)
  }
}

// 更新图表尺寸
const updateChartSize = () => {
  if (!chartContainer.value) return
  
  const containerWidth = chartContainer.value.offsetWidth
  const nodeCount = nodes.value.length
  
  chartWidth.value = Math.max(containerWidth, nodeCount * 180 + 40)
  chartHeight.value = 200
}

// 初始化
onMounted(async () => {
  if (props.instanceId) {
    await refreshWorkflow()
  } else if (props.showPreview && props.businessType) {
    await showWorkflowPreview()
  }
})
</script>

<style scoped>
.workflow-chart {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-container {
  position: relative;
  min-height: 200px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  overflow-x: auto;
}

.workflow-nodes {
  position: relative;
  height: 200px;
}

.workflow-node {
  position: absolute;
  width: 120px;
  height: 60px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #fff;
  border: 2px solid #d9d9d9;
}

.workflow-node.node-pending {
  border-color: #d9d9d9;
  background: #fafafa;
}

.workflow-node.node-processing {
  border-color: #1890ff;
  background: #e6f7ff;
}

.workflow-node.node-completed {
  border-color: #52c41a;
  background: #f6ffed;
}

.workflow-node.node-rejected {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.node-icon {
  margin-right: 8px;
  font-size: 16px;
}

.node-content {
  flex: 1;
}

.node-title {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 2px;
}

.node-assignee {
  font-size: 10px;
  color: #666;
}

.workflow-edges {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.approval-history {
  margin-top: 24px;
}

.history-item {
  padding-bottom: 8px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.history-node {
  font-weight: 500;
}

.history-time {
  font-size: 12px;
  color: #666;
}

.history-content {
  margin-bottom: 4px;
}

.history-approver {
  margin-right: 8px;
}

.history-action {
  color: #666;
}

.history-comment {
  font-size: 12px;
  color: #666;
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  margin-top: 4px;
}
</style>
