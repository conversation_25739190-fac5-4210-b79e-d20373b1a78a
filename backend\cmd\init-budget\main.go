package main

import (
	"log"

	"hospital-management/internal/config"
	"hospital-management/internal/database"
	"hospital-management/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Setup("debug", "")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("加载配置失败:", err)
	}

	// 连接数据库
	db, err := database.Connect(cfg.Database)
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	// 重新初始化预算数据
	if err := database.SeedData(db); err != nil {
		log.Fatal("初始化预算数据失败:", err)
	}

	log.Println("预算数据初始化完成！")
}
