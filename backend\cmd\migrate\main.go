package main

import (
	"log"

	"hospital-management/internal/config"
	"hospital-management/internal/database"
	"hospital-management/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Setup("debug", "")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("加载配置失败:", err)
	}

	// 连接数据库
	db, err := database.Connect(cfg.Database)
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	// 执行迁移
	migrationManager := database.NewMigrationManager(db)
	if err := migrationManager.RunMigrations(); err != nil {
		log.Fatal("数据库迁移失败:", err)
	}

	log.Println("数据库迁移完成！")
}
