import request from './request'

// 支出控制相关API接口

export interface CreateExpenseClaimRequest {
  title: string
  payeeType: 'personal' | 'corporate'
  payeeInfo: {
    type: string
    name: string
    bankName: string
    accountNo: string
    taxNo?: string
    address?: string
    phone?: string
  }
  relatedPreApprovalId?: string
  relatedContractId?: string
  details: Array<{
    expenseTypeId: string
    budgetItemId: string
    amount: number
    description: string
    invoiceData?: {
      code?: string
      number?: string
      date?: string
      amount?: number
      sellerName?: string
      sellerTaxNo?: string
      buyerName?: string
      buyerTaxNo?: string
      verified?: boolean
    }
  }>
  attachments?: Array<{
    fileName: string
    originalName: string
    filePath: string
    fileSize?: number
    fileType?: string
  }>
}

export interface ExpenseClaimResponse {
  id: string
  title: string
  applicantId: string
  applicantName: string
  departmentId: string
  departmentName: string
  totalAmount: number
  status: string
  payeeType: string
  payeeInfo: any
  workflowInstanceId?: string
  relatedPreApprovalId?: string
  relatedContractId?: string
  submittedAt?: string
  approvedAt?: string
  paidAt?: string
  details: Array<{
    id: string
    expenseTypeId: string
    expenseTypeName: string
    budgetItemId: string
    budgetItemName: string
    amount: number
    description: string
    invoiceData: any
  }>
  attachments: Array<{
    id: string
    fileName: string
    originalName: string
    filePath: string
    fileSize: number
    fileType: string
    uploadedBy: string
    createdAt: string
  }>
  createdAt: string
  updatedAt: string
}

export interface GetExpenseClaimListParams {
  status?: string
  keyword?: string
  startDate?: string
  endDate?: string
  page?: number
  pageSize?: number
}

export interface BudgetBalanceResponse {
  budgetItemId: string
  budgetItemName: string
  totalAmount: number
  usedAmount: number
  frozenAmount: number
  availableBalance: number
  controlType: string
}

export interface ExpenseTypeResponse {
  id: string
  code: string
  name: string
  description: string
  isActive: boolean
}

export interface BudgetItemResponse {
  id: string
  year: number
  subjectCode: string
  subjectName: string
  totalAmount: number
  usedAmount: number
  frozenAmount: number
  controlType: string
  expenseTypeId?: string
}

// 支出控制API
export const expenseApi = {
  // 创建报销申请
  createClaim(data: CreateExpenseClaimRequest) {
    return request.post<ExpenseClaimResponse>('/expense-claims', data)
  },

  // 获取报销申请列表
  getClaimList(params: GetExpenseClaimListParams) {
    return request.get('/expense-claims', { params })
  },

  // 获取报销申请详情
  getClaim(id: string) {
    return request.get<ExpenseClaimResponse>(`/expense-claims/${id}`)
  },

  // 删除报销申请
  deleteClaim(id: string) {
    return request.delete(`/expense-claims/${id}`)
  },

  // 撤回报销申请
  withdrawClaim(id: string) {
    return request.post(`/expense-claims/${id}/withdraw`)
  },

  // 获取预算余额
  getBudgetBalance(budgetItemId: string) {
    return request.get<BudgetBalanceResponse>(`/budget-items/${budgetItemId}/balance`)
  },

  // 获取预算科目列表
  getBudgetItems(expenseTypeId?: string) {
    const params = expenseTypeId ? { expenseTypeId } : {}
    return request.get<BudgetItemResponse[]>('/budget-items', { params })
  },

  // 获取费用类型列表
  getExpenseTypes() {
    return request.get<ExpenseTypeResponse[]>('/expense-types')
  },

  // 搜索事前申请
  searchPreApprovals(keyword: string, limit?: number) {
    return request.get('/pre-approvals/search', {
      params: { keyword, limit }
    })
  },

  // 搜索合同
  searchContracts(keyword: string, limit?: number) {
    return request.get('/contracts/search', {
      params: { keyword, limit }
    })
  }
}

export default expenseApi
