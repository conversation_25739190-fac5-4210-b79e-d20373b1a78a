<template>
  <div class="expense-claim-detail">
    <a-spin :spinning="loading">
      <!-- 基本信息 -->
      <a-descriptions title="基本信息" :column="2" bordered>
        <a-descriptions-item label="申请标题">{{ claimData.title }}</a-descriptions-item>
        <a-descriptions-item label="申请人">{{ claimData.applicantName }}</a-descriptions-item>
        <a-descriptions-item label="所属部门">{{ claimData.departmentName }}</a-descriptions-item>
        <a-descriptions-item label="申请时间">{{ formatTime(claimData.submittedAt) }}</a-descriptions-item>
        <a-descriptions-item label="总金额">
          <span style="color: #1890ff; font-weight: 500;">¥{{ claimData.totalAmount?.toFixed(2) || '0.00' }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="收款方式">
          <a-tag :color="claimData.payeeType === 'personal' ? 'blue' : 'green'">
            {{ claimData.payeeType === 'personal' ? '对私' : '对公' }}
          </a-tag>
        </a-descriptions-item>
      </a-descriptions>

      <!-- 收款信息 -->
      <a-descriptions title="收款信息" :column="2" bordered style="margin-top: 16px;">
        <a-descriptions-item label="收款人/单位">{{ claimData.payeeInfo?.name || '-' }}</a-descriptions-item>
        <a-descriptions-item label="开户银行">{{ claimData.payeeInfo?.bank || '-' }}</a-descriptions-item>
        <a-descriptions-item label="银行账号" :span="2">{{ claimData.payeeInfo?.account || '-' }}</a-descriptions-item>
      </a-descriptions>

      <!-- 关联信息 -->
      <div v-if="claimData.relatedPreApprovalId || claimData.relatedContractId" style="margin-top: 16px;">
        <a-descriptions title="关联信息" :column="2" bordered>
          <a-descriptions-item v-if="claimData.relatedPreApprovalId" label="关联事前申请">
            <a-button type="link" size="small" @click="viewRelatedPreApproval">
              {{ claimData.relatedPreApprovalTitle || claimData.relatedPreApprovalId }}
            </a-button>
          </a-descriptions-item>
          <a-descriptions-item v-if="claimData.relatedContractId" label="关联合同">
            <a-button type="link" size="small" @click="viewRelatedContract">
              {{ claimData.relatedContractTitle || claimData.relatedContractId }}
            </a-button>
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 费用明细 -->
      <div style="margin-top: 16px;">
        <h3>费用明细</h3>
        <a-table
          :columns="detailColumns"
          :data-source="claimData.details || []"
          :pagination="false"
          size="small"
          bordered
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'amount'">
              <span style="color: #1890ff;">¥{{ record.amount?.toFixed(2) || '0.00' }}</span>
            </template>
            <template v-if="column.key === 'availableBalance'">
              <span :style="{ color: record.availableBalance >= record.amount ? '#52c41a' : '#ff4d4f' }">
                ¥{{ record.availableBalance?.toFixed(2) || '0.00' }}
              </span>
            </template>
            <template v-if="column.key === 'invoiceInfo'">
              <a-button v-if="record.invoiceInfo" type="link" size="small" @click="viewInvoice(record)">
                查看发票
              </a-button>
              <span v-else>-</span>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 附件信息 -->
      <div v-if="claimData.attachments && claimData.attachments.length > 0" style="margin-top: 16px;">
        <h3>附件信息</h3>
        <a-list
          :data-source="claimData.attachments"
          size="small"
          bordered
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <template #actions>
                <a-button type="link" size="small" @click="downloadAttachment(item)">下载</a-button>
                <a-button type="link" size="small" @click="previewAttachment(item)">预览</a-button>
              </template>
              <a-list-item-meta>
                <template #title>
                  <a @click="previewAttachment(item)">{{ item.fileName }}</a>
                </template>
                <template #description>
                  {{ formatFileSize(item.fileSize) }} | {{ formatTime(item.uploadTime) }}
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </div>

      <!-- 审批流程预览 -->
      <div style="margin-top: 16px;">
        <h3>审批流程</h3>
        <WorkflowChart
          :business-type="'expense_claim'"
          :business-amount="claimData.totalAmount"
          :department-id="claimData.departmentId"
          :show-preview="true"
          :show-history="false"
          title=""
        />
      </div>
    </a-spin>

    <!-- 发票详情弹窗 -->
    <a-modal
      v-model:visible="invoiceModalVisible"
      title="发票详情"
      width="600px"
      :footer="null"
    >
      <div v-if="currentInvoice">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="发票代码">{{ currentInvoice.code }}</a-descriptions-item>
          <a-descriptions-item label="发票号码">{{ currentInvoice.number }}</a-descriptions-item>
          <a-descriptions-item label="开票日期">{{ currentInvoice.date }}</a-descriptions-item>
          <a-descriptions-item label="发票金额">¥{{ currentInvoice.amount?.toFixed(2) }}</a-descriptions-item>
          <a-descriptions-item label="销售方名称" :span="2">{{ currentInvoice.sellerName }}</a-descriptions-item>
          <a-descriptions-item label="销售方税号" :span="2">{{ currentInvoice.sellerTaxId }}</a-descriptions-item>
          <a-descriptions-item label="购买方名称" :span="2">{{ currentInvoice.buyerName }}</a-descriptions-item>
          <a-descriptions-item label="购买方税号" :span="2">{{ currentInvoice.buyerTaxId }}</a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 附件预览弹窗 -->
    <a-modal
      v-model:visible="attachmentModalVisible"
      :title="currentAttachment?.fileName"
      width="800px"
      :footer="null"
    >
      <div v-if="currentAttachment" style="text-align: center;">
        <img 
          v-if="isImageFile(currentAttachment.fileName)"
          :src="currentAttachment.previewUrl" 
          style="max-width: 100%; max-height: 500px;"
          alt="附件预览"
        />
        <div v-else style="padding: 40px; color: #666;">
          <FileOutlined style="font-size: 48px; margin-bottom: 16px;" />
          <div>{{ currentAttachment.fileName }}</div>
          <div style="margin-top: 8px;">
            <a-button type="primary" @click="downloadAttachment(currentAttachment)">下载文件</a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { FileOutlined } from '@ant-design/icons-vue'
import { expenseApi } from '@/api/expense'
import WorkflowChart from '@/components/WorkflowChart.vue'

// Props
interface Props {
  businessId: string
  businessType: string
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: true
})

// 响应式数据
const loading = ref(false)
const invoiceModalVisible = ref(false)
const attachmentModalVisible = ref(false)
const claimData = ref<any>({})
const currentInvoice = ref<any>(null)
const currentAttachment = ref<any>(null)

// 表格列定义
const detailColumns = [
  { title: '费用类型', dataIndex: 'expenseTypeName', key: 'expenseTypeName', width: 120 },
  { title: '预算科目', dataIndex: 'budgetItemName', key: 'budgetItemName', width: 150 },
  { title: '可用余额', key: 'availableBalance', width: 120 },
  { title: '报销金额', key: 'amount', width: 120 },
  { title: '费用说明', dataIndex: 'description', key: 'description', ellipsis: true },
  { title: '发票信息', key: 'invoiceInfo', width: 100 }
]

// 格式化时间
const formatTime = (time: string) => {
  return time ? new Date(time).toLocaleString() : '-'
}

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) return `${size}B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
  return `${(size / (1024 * 1024)).toFixed(1)}MB`
}

// 判断是否为图片文件
const isImageFile = (fileName: string) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return imageExtensions.includes(ext)
}

// 加载报销单详情
const loadClaimDetail = async () => {
  loading.value = true
  try {
    const response = await expenseApi.getExpenseClaim(props.businessId)
    claimData.value = response
  } catch (error) {
    console.error('加载报销单详情失败:', error)
    message.error('加载报销单详情失败')
  } finally {
    loading.value = false
  }
}

// 查看发票
const viewInvoice = (record: any) => {
  currentInvoice.value = record.invoiceInfo
  invoiceModalVisible.value = true
}

// 查看关联事前申请
const viewRelatedPreApproval = () => {
  message.info('查看关联事前申请功能开发中...')
}

// 查看关联合同
const viewRelatedContract = () => {
  message.info('查看关联合同功能开发中...')
}

// 下载附件
const downloadAttachment = (attachment: any) => {
  message.info('下载附件功能开发中...')
}

// 预览附件
const previewAttachment = (attachment: any) => {
  currentAttachment.value = attachment
  attachmentModalVisible.value = true
}

// 初始化
onMounted(() => {
  if (props.businessId) {
    loadClaimDetail()
  }
})
</script>

<style scoped>
.expense-claim-detail {
  padding: 16px;
}

.expense-claim-detail h3 {
  margin-bottom: 16px;
  color: #262626;
  font-weight: 500;
}
</style>
