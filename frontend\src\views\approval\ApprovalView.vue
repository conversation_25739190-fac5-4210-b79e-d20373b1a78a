<template>
  <div class="approval-view">
    <div class="approval-header">
      <a-breadcrumb>
        <a-breadcrumb-item>
          <router-link to="/approval/tasks">待办审批</router-link>
        </a-breadcrumb-item>
        <a-breadcrumb-item>审批详情</a-breadcrumb-item>
      </a-breadcrumb>
      
      <div class="header-actions">
        <a-space>
          <a-button @click="goBack">返回</a-button>
          <a-button type="primary" @click="refreshData">刷新</a-button>
        </a-space>
      </div>
    </div>

    <div class="approval-content">
      <a-row :gutter="24">
        <!-- 左侧：业务单据详情 -->
        <a-col :span="16">
          <a-card title="申请详情" :loading="detailLoading">
            <component 
              :is="businessDetailComponent" 
              :business-id="businessId"
              :business-type="businessType"
              :readonly="true"
            />
          </a-card>
        </a-col>

        <!-- 右侧：审批操作区 -->
        <a-col :span="8">
          <!-- 流程图 -->
          <a-card title="审批流程" class="workflow-card" :loading="workflowLoading">
            <WorkflowChart
              :instance-id="instanceId"
              :show-history="false"
              title=""
            />
          </a-card>

          <!-- 审批操作 -->
          <a-card title="审批操作" class="approval-actions-card">
            <a-form layout="vertical">
              <a-form-item label="审批意见">
                <a-textarea
                  v-model:value="approvalForm.comment"
                  placeholder="请输入审批意见（驳回时必填）"
                  :rows="4"
                  :maxlength="500"
                  show-count
                />
              </a-form-item>
              
              <a-form-item>
                <a-space style="width: 100%">
                  <a-button 
                    type="primary" 
                    :loading="approvalLoading"
                    @click="handleApproval('approve')"
                    style="flex: 1"
                  >
                    <CheckOutlined />
                    同意
                  </a-button>
                  <a-button 
                    danger 
                    :loading="approvalLoading"
                    @click="handleApproval('reject')"
                    style="flex: 1"
                  >
                    <CloseOutlined />
                    驳回
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 审批历史 -->
          <a-card title="审批历史" class="approval-history-card">
            <a-timeline>
              <a-timeline-item
                v-for="history in approvalHistory"
                :key="history.id"
                :color="getHistoryColor(history.action)"
              >
                <template #dot>
                  <CheckCircleOutlined v-if="history.action === 'approve'" style="color: #52c41a" />
                  <CloseCircleOutlined v-if="history.action === 'reject'" style="color: #ff4d4f" />
                  <PlayCircleOutlined v-if="history.action === 'start'" style="color: #1890ff" />
                </template>
                <div class="history-item">
                  <div class="history-header">
                    <span class="history-node">{{ history.nodeName }}</span>
                    <span class="history-time">{{ formatTime(history.createdAt) }}</span>
                  </div>
                  <div class="history-content">
                    <span class="history-approver">{{ history.approverName }}</span>
                    <span class="history-action">{{ getActionText(history.action) }}</span>
                  </div>
                  <div v-if="history.comment" class="history-comment">
                    {{ history.comment }}
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  CheckOutlined,
  CloseOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  PlayCircleOutlined
} from '@ant-design/icons-vue'
import { workflowApi } from '@/api/workflow'
import WorkflowChart from '@/components/WorkflowChart.vue'
import type { ApprovalHistory, ApprovalFormData } from '@/types/workflow'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const detailLoading = ref(false)
const workflowLoading = ref(false)
const approvalLoading = ref(false)
const approvalHistory = ref<ApprovalHistory[]>([])

// 表单数据
const approvalForm = ref<ApprovalFormData>({
  instanceId: '',
  action: 'approve',
  comment: ''
})

// 从路由参数获取数据
const instanceId = computed(() => route.params.instanceId as string)
const businessId = computed(() => route.query.businessId as string)
const businessType = computed(() => route.query.businessType as string)

// 动态组件导入
import GeneralExpenseClaimDetail from '@/components/business/GeneralExpenseClaimDetail.vue'
import ContractDetail from '@/components/business/ContractDetail.vue'
import PreApprovalDetail from '@/components/business/PreApprovalDetail.vue'

// 动态组件
const businessDetailComponent = computed(() => {
  const componentMap: Record<string, any> = {
    expense_claim: GeneralExpenseClaimDetail,
    contract: ContractDetail,
    pre_approval: PreApprovalDetail
  }
  return componentMap[businessType.value] || 'div'
})

// 获取历史记录颜色
const getHistoryColor = (action: string) => {
  const colorMap: Record<string, string> = {
    start: 'blue',
    approve: 'green',
    reject: 'red'
  }
  return colorMap[action] || 'gray'
}

// 获取动作文本
const getActionText = (action: string) => {
  const textMap: Record<string, string> = {
    start: '发起申请',
    approve: '审批通过',
    reject: '审批驳回'
  }
  return textMap[action] || action
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

// 处理审批
const handleApproval = async (action: 'approve' | 'reject') => {
  // 驳回时必须填写意见
  if (action === 'reject' && !approvalForm.value.comment?.trim()) {
    message.warning('驳回时必须填写审批意见')
    return
  }

  approvalLoading.value = true
  try {
    await workflowApi.processApproval({
      instanceId: instanceId.value,
      action,
      comment: approvalForm.value.comment
    })

    message.success(`${action === 'approve' ? '审批通过' : '审批驳回'}成功`)
    
    // 刷新数据
    await loadApprovalHistory()
    
    // 延迟返回列表页
    setTimeout(() => {
      router.push('/approval/tasks')
    }, 1500)
  } catch (error) {
    console.error('审批失败:', error)
    message.error('审批失败')
  } finally {
    approvalLoading.value = false
  }
}

// 加载审批历史
const loadApprovalHistory = async () => {
  try {
    // 模拟数据，实际应该调用API
    approvalHistory.value = [
      {
        id: '1',
        instanceId: instanceId.value,
        nodeId: 'start',
        nodeName: '发起申请',
        approverId: 'user1',
        approverName: '张三',
        action: 'start',
        comment: '',
        createdAt: new Date(Date.now() - 86400000).toISOString()
      },
      {
        id: '2',
        instanceId: instanceId.value,
        nodeId: 'dept_head',
        nodeName: '部门负责人审批',
        approverId: 'user2',
        approverName: '李四',
        action: 'approve',
        comment: '同意申请',
        createdAt: new Date(Date.now() - 3600000).toISOString()
      }
    ]
  } catch (error) {
    console.error('加载审批历史失败:', error)
  }
}

// 刷新数据
const refreshData = async () => {
  await Promise.all([
    loadApprovalHistory()
  ])
}

// 返回
const goBack = () => {
  router.go(-1)
}

// 初始化
onMounted(async () => {
  approvalForm.value.instanceId = instanceId.value
  await refreshData()
})
</script>

<style scoped>
.approval-view {
  padding: 24px;
  min-height: 100vh;
  background: #f5f5f5;
}

.approval-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: #fff;
  padding: 16px 24px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.approval-content {
  min-height: calc(100vh - 120px);
}

.workflow-card {
  margin-bottom: 16px;
}

.approval-actions-card {
  margin-bottom: 16px;
}

.approval-history-card {
  max-height: 400px;
  overflow-y: auto;
}

.history-item {
  padding-bottom: 8px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.history-node {
  font-weight: 500;
}

.history-time {
  font-size: 12px;
  color: #666;
}

.history-content {
  margin-bottom: 4px;
}

.history-approver {
  margin-right: 8px;
}

.history-action {
  color: #666;
}

.history-comment {
  font-size: 12px;
  color: #666;
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  margin-top: 4px;
}
</style>
