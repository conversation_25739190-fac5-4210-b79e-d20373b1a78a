package dto

import (
	"time"

	"github.com/google/uuid"
)

// ProcessApprovalRequest 处理审批请求
type ProcessApprovalRequest struct {
	InstanceID string `json:"instanceId" binding:"required"`
	Action     string `json:"action" binding:"required,oneof=approve reject"`
	Comment    string `json:"comment"`
}

// WorkflowNodeResponse 工作流节点响应
type WorkflowNodeResponse struct {
	ID       string `json:"id"`
	Label    string `json:"label"`
	Status   string `json:"status"` // pending, processing, completed, rejected
	Assignee string `json:"assignee"`
}

// WorkflowEdgeResponse 工作流边响应
type WorkflowEdgeResponse struct {
	Source string `json:"source"`
	Target string `json:"target"`
}

// WorkflowInstanceResponse 工作流实例响应
type WorkflowInstanceResponse struct {
	ID            uuid.UUID              `json:"id"`
	DefinitionKey string                 `json:"definitionKey"`
	BusinessID    uuid.UUID              `json:"businessId"`
	BusinessType  string                 `json:"businessType"`
	Status        string                 `json:"status"`
	StartTime     time.Time              `json:"startTime"`
	EndTime       *time.Time             `json:"endTime,omitempty"`
	Nodes         []WorkflowNodeResponse `json:"nodes"`
	Edges         []WorkflowEdgeResponse `json:"edges"`
}

// WorkflowPreviewResponse 工作流预览响应
type WorkflowPreviewResponse struct {
	Nodes []WorkflowNodeResponse `json:"nodes"`
	Edges []WorkflowEdgeResponse `json:"edges"`
}

// StartWorkflowRequest 启动工作流请求
type StartWorkflowRequest struct {
	DefinitionKey string                 `json:"definitionKey" binding:"required"`
	BusinessID    uuid.UUID              `json:"businessId" binding:"required"`
	BusinessType  string                 `json:"businessType" binding:"required"`
	StartUserID   uuid.UUID              `json:"startUserId" binding:"required"`
	Variables     map[string]interface{} `json:"variables,omitempty"`
}

// StartWorkflowResponse 启动工作流响应
type StartWorkflowResponse struct {
	InstanceID    string `json:"instanceId"`
	CurrentNodeID string `json:"currentNodeId"`
	Status        string `json:"status"`
}

// TerminateWorkflowRequest 终止工作流请求
type TerminateWorkflowRequest struct {
	Reason string `json:"reason" binding:"required"`
}

// ApprovalTaskResponse 待办任务响应
type ApprovalTaskResponse struct {
	ID                  string     `json:"id"`
	Type                string     `json:"type"`
	Title               string     `json:"title"`
	Applicant           string     `json:"applicant"`
	ApplicantName       string     `json:"applicantName"`
	Summary             string     `json:"summary"`
	Amount              *float64   `json:"amount,omitempty"`
	SubmissionTime      time.Time  `json:"submissionTime"`
	UrgentLevel         string     `json:"urgentLevel,omitempty"`
	BusinessID          string     `json:"businessId"`
	BusinessType        string     `json:"businessType"`
	WorkflowInstanceID  string     `json:"workflowInstanceId"`
	CurrentNodeID       string     `json:"currentNodeId"`
	Link                string     `json:"link"`
}

// ApprovalHistoryResponse 审批历史响应
type ApprovalHistoryResponse struct {
	ID            string    `json:"id"`
	InstanceID    string    `json:"instanceId"`
	NodeID        string    `json:"nodeId"`
	NodeName      string    `json:"nodeName"`
	ApproverID    string    `json:"approverId"`
	ApproverName  string    `json:"approverName"`
	Action        string    `json:"action"`
	Comment       string    `json:"comment"`
	CreatedAt     time.Time `json:"createdAt"`
	Title         string    `json:"title,omitempty"`
	ApplicantName string    `json:"applicantName,omitempty"`
	BusinessType  string    `json:"businessType,omitempty"`
	Amount        *float64  `json:"amount,omitempty"`
	Duration      string    `json:"duration,omitempty"`
}

// WorkflowDefinitionRequest 工作流定义请求
type WorkflowDefinitionRequest struct {
	Key         string                    `json:"key" binding:"required"`
	Name        string                    `json:"name" binding:"required"`
	Description string                    `json:"description"`
	Status      string                    `json:"status" binding:"required,oneof=active inactive"`
	Nodes       []WorkflowNodeDefinition  `json:"nodes"`
}

// WorkflowNodeDefinition 工作流节点定义
type WorkflowNodeDefinition struct {
	NodeID     string `json:"nodeId" binding:"required"`
	NodeType   string `json:"nodeType" binding:"required,oneof=start end approval gateway"`
	NodeName   string `json:"nodeName" binding:"required"`
	NextNodes  string `json:"nextNodes"`
	Assignee   string `json:"assignee"`
	Conditions string `json:"conditions,omitempty"`
}

// WorkflowDefinitionResponse 工作流定义响应
type WorkflowDefinitionResponse struct {
	ID          string                   `json:"id"`
	Key         string                   `json:"key"`
	Name        string                   `json:"name"`
	Description string                   `json:"description"`
	Version     int                      `json:"version"`
	Status      string                   `json:"status"`
	CreatedAt   time.Time                `json:"createdAt"`
	UpdatedAt   time.Time                `json:"updatedAt"`
	Nodes       []WorkflowNodeDefinition `json:"nodes"`
}

// WorkflowInstanceListResponse 工作流实例列表响应
type WorkflowInstanceListResponse struct {
	ID            string     `json:"id"`
	DefinitionKey string     `json:"definitionKey"`
	BusinessID    string     `json:"businessId"`
	BusinessType  string     `json:"businessType"`
	Status        string     `json:"status"`
	StartTime     time.Time  `json:"startTime"`
	EndTime       *time.Time `json:"endTime,omitempty"`
	StartUserName string     `json:"startUserName"`
	CurrentNode   string     `json:"currentNode,omitempty"`
}

// MonitorStatsResponse 监控统计响应
type MonitorStatsResponse struct {
	RunningInstances   int     `json:"runningInstances"`
	PendingTasks       int     `json:"pendingTasks"`
	TodayCompleted     int     `json:"todayCompleted"`
	OvertimeInstances  int     `json:"overtimeInstances"`
	AvgProcessTime     float64 `json:"avgProcessTime"`
	ApprovalRate       float64 `json:"approvalRate"`
	SystemResponseTime int     `json:"systemResponseTime"`
}

// ExceptionInstanceResponse 异常实例响应
type ExceptionInstanceResponse struct {
	ID                 string  `json:"id"`
	BusinessType       string  `json:"businessType"`
	Title              string  `json:"title"`
	CurrentNodeName    string  `json:"currentNodeName,omitempty"`
	CurrentAssignee    string  `json:"currentAssignee,omitempty"`
	OvertimeHours      *int    `json:"overtimeHours,omitempty"`
	StuckDays          *int    `json:"stuckDays,omitempty"`
	ErrorType          string  `json:"errorType,omitempty"`
	ErrorMessage       string  `json:"errorMessage,omitempty"`
	StartTime          time.Time `json:"startTime"`
}

// UrgeProcessRequest 催办请求
type UrgeProcessRequest struct {
	Message string `json:"message" binding:"required"`
}

// GetPendingTasksRequest 获取待办任务请求
type GetPendingTasksRequest struct {
	BusinessType string `form:"businessType"`
	UrgentLevel  string `form:"urgentLevel"`
	Keyword      string `form:"keyword"`
	Page         int    `form:"page,default=1"`
	PageSize     int    `form:"pageSize,default=10"`
}

// GetApprovalHistoryRequest 获取审批历史请求
type GetApprovalHistoryRequest struct {
	BusinessType string  `form:"businessType"`
	Action       string  `form:"action"`
	Approver     string  `form:"approver"`
	Applicant    string  `form:"applicant"`
	StartDate    string  `form:"startDate"`
	EndDate      string  `form:"endDate"`
	MinAmount    float64 `form:"minAmount"`
	MaxAmount    float64 `form:"maxAmount"`
	Page         int     `form:"page,default=1"`
	PageSize     int     `form:"pageSize,default=10"`
}

// GetWorkflowDefinitionsRequest 获取工作流定义请求
type GetWorkflowDefinitionsRequest struct {
	Status   string `form:"status"`
	Keyword  string `form:"keyword"`
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"pageSize,default=10"`
}

// GetWorkflowInstancesRequest 获取工作流实例请求
type GetWorkflowInstancesRequest struct {
	DefinitionKey string `form:"definitionKey"`
	Status        string `form:"status"`
	BusinessType  string `form:"businessType"`
	Page          int    `form:"page,default=1"`
	PageSize      int    `form:"pageSize,default=10"`
}
