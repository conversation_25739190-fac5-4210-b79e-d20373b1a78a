<template>
  <a-modal
    v-model:visible="visible"
    :title="isEdit ? '编辑报销申请' : '新建报销申请'"
    width="800px"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <!-- 基本信息 -->
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="申请事由" name="title">
            <a-input v-model:value="formData.title" placeholder="请输入申请事由" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 收款人信息 -->
      <a-divider>收款人信息</a-divider>
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="收款类型" name="payeeType">
            <a-select v-model:value="formData.payeeType" placeholder="请选择收款类型">
              <a-select-option value="personal">对私</a-select-option>
              <a-select-option value="corporate">对公</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="收款人姓名" name="payeeInfo.name">
            <a-input v-model:value="formData.payeeInfo.name" placeholder="请输入收款人姓名" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="开户银行" name="payeeInfo.bankName">
            <a-input v-model:value="formData.payeeInfo.bankName" placeholder="请输入开户银行" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="银行账号" name="payeeInfo.accountNo">
            <a-input v-model:value="formData.payeeInfo.accountNo" placeholder="请输入银行账号" />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="formData.payeeType === 'corporate'">
          <a-form-item label="税号" name="payeeInfo.taxNo">
            <a-input v-model:value="formData.payeeInfo.taxNo" placeholder="请输入税号" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 关联信息 -->
      <a-divider>关联信息（可选）</a-divider>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="关联事前申请">
            <a-select
              v-model:value="formData.relatedPreApprovalId"
              placeholder="请选择事前申请"
              show-search
              :filter-option="false"
              :not-found-content="preApprovalSearching ? '搜索中...' : '暂无数据'"
              @search="searchPreApprovals"
              allowClear
            >
              <a-select-option
                v-for="item in preApprovalOptions"
                :key="item.id"
                :value="item.id"
              >
                {{ item.title }} (¥{{ item.amount }})
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="关联合同">
            <a-select
              v-model:value="formData.relatedContractId"
              placeholder="请选择合同"
              show-search
              :filter-option="false"
              :not-found-content="contractSearching ? '搜索中...' : '暂无数据'"
              @search="searchContracts"
              allowClear
            >
              <a-select-option
                v-for="item in contractOptions"
                :key="item.id"
                :value="item.id"
              >
                {{ item.name }} ({{ item.contractNo }})
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 费用明细 -->
      <a-divider>费用明细</a-divider>
      <div class="expense-details">
        <div v-for="(detail, index) in formData.details" :key="index" class="detail-item">
          <a-row :gutter="16" align="middle">
            <a-col :span="5">
              <a-form-item :name="['details', index, 'expenseTypeId']" :rules="[{ required: true, message: '请选择费用类型' }]">
                <a-select v-model:value="detail.expenseTypeId" placeholder="费用类型" @change="onExpenseTypeChange(index)">
                  <a-select-option v-for="type in expenseTypes" :key="type.id" :value="type.id">
                    {{ type.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="5">
              <a-form-item :name="['details', index, 'budgetItemId']" :rules="[{ required: true, message: '请选择预算科目' }]">
                <a-select v-model:value="detail.budgetItemId" placeholder="预算科目" @change="onBudgetItemChange(index)">
                  <a-select-option v-for="item in budgetItems" :key="item.id" :value="item.id">
                    {{ item.subjectName }} (余额: ¥{{ (item.totalAmount - item.usedAmount - item.frozenAmount).toFixed(2) }})
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="4">
              <a-form-item :name="['details', index, 'amount']" :rules="[{ required: true, message: '请输入金额' }]">
                <a-input-number
                  v-model:value="detail.amount"
                  placeholder="金额"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                  @change="calculateTotal"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item :name="['details', index, 'description']">
                <a-input v-model:value="detail.description" placeholder="费用说明" />
              </a-form-item>
            </a-col>
            <a-col :span="4">
              <a-space>
                <a-button type="link" size="small" @click="addDetail" v-if="index === formData.details.length - 1">
                  <PlusOutlined />
                </a-button>
                <a-button type="link" size="small" danger @click="removeDetail(index)" v-if="formData.details.length > 1">
                  <MinusOutlined />
                </a-button>
              </a-space>
            </a-col>
          </a-row>
        </div>
      </div>

      <!-- 总金额 -->
      <a-row>
        <a-col :span="24" style="text-align: right;">
          <strong>总金额: ¥{{ totalAmount.toFixed(2) }}</strong>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, MinusOutlined } from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'
import { expenseApi } from '@/api/expense'
import type { ExpenseClaim, ExpenseClaimDetail, ExpenseType, BudgetItem } from '@/types/expense'

// Props
interface Props {
  visible: boolean
  claimData?: ExpenseClaim | null
}

const props = withDefaults(defineProps<Props>(), {
  claimData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()
const expenseTypes = ref<ExpenseType[]>([])
const budgetItems = ref<BudgetItem[]>([])
const preApprovalOptions = ref([])
const contractOptions = ref([])
const preApprovalSearching = ref(false)
const contractSearching = ref(false)

// 表单数据
const formData = reactive<ExpenseClaim>({
  title: '',
  payeeType: 'personal',
  payeeInfo: {
    type: 'personal',
    name: '',
    bankName: '',
    accountNo: '',
    taxNo: ''
  },
  relatedPreApprovalId: undefined,
  relatedContractId: undefined,
  details: [{
    expenseTypeId: '',
    budgetItemId: '',
    amount: 0,
    description: ''
  }],
  attachments: [],
  totalAmount: 0
})

// 表单验证规则
const rules = {
  title: [{ required: true, message: '请输入申请事由' }],
  payeeType: [{ required: true, message: '请选择收款类型' }],
  'payeeInfo.name': [{ required: true, message: '请输入收款人姓名' }],
  'payeeInfo.bankName': [{ required: true, message: '请输入开户银行' }],
  'payeeInfo.accountNo': [{ required: true, message: '请输入银行账号' }]
}

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.claimData?.id)

const totalAmount = computed(() => {
  return formData.details.reduce((sum, detail) => sum + (detail.amount || 0), 0)
})

// 监听总金额变化
watch(totalAmount, (newVal) => {
  formData.totalAmount = newVal
})

// 监听弹窗显示
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initForm()
    loadExpenseTypes()
    loadBudgetItems()
  }
})

// 初始化表单
const initForm = () => {
  if (props.claimData) {
    Object.assign(formData, props.claimData)
  } else {
    // 重置表单
    Object.assign(formData, {
      title: '',
      payeeType: 'personal',
      payeeInfo: {
        type: 'personal',
        name: '',
        bankName: '',
        accountNo: '',
        taxNo: ''
      },
      relatedPreApprovalId: undefined,
      relatedContractId: undefined,
      details: [{
        expenseTypeId: '',
        budgetItemId: '',
        amount: 0,
        description: ''
      }],
      attachments: [],
      totalAmount: 0
    })
  }
}

// 加载费用类型
const loadExpenseTypes = async () => {
  try {
    expenseTypes.value = await expenseApi.getExpenseTypes()
  } catch (error) {
    console.error('加载费用类型失败:', error)
  }
}

// 加载预算科目
const loadBudgetItems = async () => {
  try {
    budgetItems.value = await expenseApi.getBudgetItems()
  } catch (error) {
    console.error('加载预算科目失败:', error)
  }
}

// 费用类型变化
const onExpenseTypeChange = (index: number) => {
  // 清空预算科目选择
  formData.details[index].budgetItemId = ''
}

// 预算科目变化
const onBudgetItemChange = (index: number) => {
  // 可以在这里添加预算余额检查逻辑
}

// 计算总金额
const calculateTotal = () => {
  // 总金额会通过computed自动计算
}

// 添加明细
const addDetail = () => {
  formData.details.push({
    expenseTypeId: '',
    budgetItemId: '',
    amount: 0,
    description: ''
  })
}

// 删除明细
const removeDetail = (index: number) => {
  formData.details.splice(index, 1)
}

// 搜索事前申请
const searchPreApprovals = async (keyword: string) => {
  if (!keyword) return
  
  preApprovalSearching.value = true
  try {
    preApprovalOptions.value = await expenseApi.searchPreApprovals(keyword, 10)
  } catch (error) {
    console.error('搜索事前申请失败:', error)
  } finally {
    preApprovalSearching.value = false
  }
}

// 搜索合同
const searchContracts = async (keyword: string) => {
  if (!keyword) return
  
  contractSearching.value = true
  try {
    contractOptions.value = await expenseApi.searchContracts(keyword, 10)
  } catch (error) {
    console.error('搜索合同失败:', error)
  } finally {
    contractSearching.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    loading.value = true
    
    if (isEdit.value) {
      // TODO: 编辑逻辑
      message.success('编辑成功')
    } else {
      await expenseApi.createClaim(formData)
      message.success('创建成功')
    }
    
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    loading.value = false
  }
}

// 取消
const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped>
.expense-details {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.detail-item {
  margin-bottom: 16px;
}

.detail-item:last-child {
  margin-bottom: 0;
}
</style>
