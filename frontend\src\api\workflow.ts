import request from './request'

// 工作流相关API接口

export interface ProcessApprovalRequest {
  instanceId: string
  action: 'approve' | 'reject'
  comment?: string
}

export interface WorkflowNodeResponse {
  id: string
  label: string
  status: 'pending' | 'processing' | 'completed' | 'rejected'
  assignee: string
}

export interface WorkflowEdgeResponse {
  source: string
  target: string
}

export interface WorkflowInstanceResponse {
  id: string
  definitionKey: string
  businessId: string
  businessType: string
  status: string
  startTime: string
  endTime?: string
  nodes: WorkflowNodeResponse[]
  edges: WorkflowEdgeResponse[]
}

export interface WorkflowPreviewResponse {
  nodes: WorkflowNodeResponse[]
  edges: WorkflowEdgeResponse[]
}

export interface StartWorkflowRequest {
  definitionKey: string
  businessId: string
  businessType: string
  startUserId: string
  variables?: Record<string, any>
}

export interface StartWorkflowResponse {
  instanceId: string
  currentNodeId: string
  status: string
}

export interface TerminateWorkflowRequest {
  reason: string
}

// 工作流API
export const workflowApi = {
  // 处理审批
  processApproval(data: ProcessApprovalRequest) {
    return request.post('/approvals', data)
  },

  // 获取工作流实例详情
  getWorkflowInstance(instanceId: string) {
    return request.get<WorkflowInstanceResponse>(`/workflow/instance/${instanceId}`)
  },

  // 获取审批流程预览
  getWorkflowPreview(params: {
    type: string
    amount?: number
    departmentId?: string
  }) {
    return request.get<WorkflowPreviewResponse>('/workflow/preview', { params })
  },

  // 终止工作流实例
  terminateWorkflow(instanceId: string, data: TerminateWorkflowRequest) {
    return request.post(`/workflow/instance/${instanceId}/terminate`, data)
  },

  // 启动工作流
  startWorkflow(data: StartWorkflowRequest) {
    return request.post<StartWorkflowResponse>('/workflow/start', data)
  },

  // 获取待办任务列表
  getPendingTasks(params?: {
    businessType?: string
    urgentLevel?: string
    keyword?: string
    page?: number
    pageSize?: number
  }) {
    return request.get('/workflow/tasks/pending', { params })
  },

  // 获取审批历史
  getApprovalHistory(params?: {
    businessType?: string
    action?: string
    approver?: string
    applicant?: string
    startDate?: string
    endDate?: string
    minAmount?: number
    maxAmount?: number
    page?: number
    pageSize?: number
  }) {
    return request.get('/workflow/approval-history', { params })
  },

  // 获取工作流定义列表
  getWorkflowDefinitions(params?: {
    status?: string
    keyword?: string
    page?: number
    pageSize?: number
  }) {
    return request.get('/workflow/definitions', { params })
  },

  // 创建工作流定义
  createWorkflowDefinition(data: any) {
    return request.post('/workflow/definitions', data)
  },

  // 更新工作流定义
  updateWorkflowDefinition(id: string, data: any) {
    return request.put(`/workflow/definitions/${id}`, data)
  },

  // 删除工作流定义
  deleteWorkflowDefinition(id: string) {
    return request.delete(`/workflow/definitions/${id}`)
  },

  // 获取工作流实例列表
  getWorkflowInstances(params?: {
    definitionKey?: string
    status?: string
    businessType?: string
    page?: number
    pageSize?: number
  }) {
    return request.get('/workflow/instances', { params })
  },

  // 获取监控统计数据
  getMonitorStats() {
    return request.get('/workflow/monitor/stats')
  },

  // 获取异常实例
  getExceptionInstances(type: 'overtime' | 'stuck' | 'error') {
    return request.get(`/workflow/monitor/exceptions/${type}`)
  },

  // 催办处理
  urgeProcess(instanceId: string, message: string) {
    return request.post(`/workflow/instance/${instanceId}/urge`, { message })
  },

  // 重试实例
  retryInstance(instanceId: string) {
    return request.post(`/workflow/instance/${instanceId}/retry`)
  }
}

export default workflowApi
